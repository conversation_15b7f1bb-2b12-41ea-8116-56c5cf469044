.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 750rpx;
  height: 1786rpx;
  overflow: hidden;
  .group_1 {
    background-image: linear-gradient(180deg, rgba(62, 200, 174, 1) 82.399964%);
    width: 750rpx;
    height: 518rpx;
    .group_2 {
      width: 688rpx;
      height: 38rpx;
      margin: 14rpx 0 0 32rpx;
      .text_1 {
        width: 64rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 24rpx;
        font-family: Inter-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 24rpx;
      }
      .thumbnail_1 {
        width: 36rpx;
        height: 36rpx;
        margin-left: 502rpx;
      }
      .thumbnail_2 {
        width: 36rpx;
        height: 36rpx;
        margin-left: 6rpx;
      }
      .thumbnail_3 {
        width: 38rpx;
        height: 38rpx;
        margin-left: 6rpx;
      }
    }
    .group_3 {
      width: 702rpx;
      height: 64rpx;
      margin: 34rpx 0 0 36rpx;
      .thumbnail_4 {
        width: 18rpx;
        height: 34rpx;
        margin-top: 16rpx;
      }
      .text_2 {
        width: 128rpx;
        height: 44rpx;
        overflow-wrap: break-word;
        color: rgba(0, 0, 0, 1);
        font-size: 32rpx;
        font-family: Inter-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 44rpx;
        margin: 10rpx 0 0 22rpx;
      }
      .image_1 {
        width: 174rpx;
        height: 64rpx;
        margin-left: 360rpx;
      }
    }
    .group_4 {
      background-color: rgba(255, 255, 255, 0.73);
      border-radius: 10px 10px 0px 0px;
      position: relative;
      width: 702rpx;
      height: 278rpx;
      border: 2px solid rgba(255, 255, 255, 0.37);
      margin: 48rpx 0 42rpx 24rpx;
      .text-group_1 {
        width: 158rpx;
        height: 114rpx;
        margin: 44rpx 0 0 36rpx;
        .text_3 {
          width: 150rpx;
          height: 32rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 26rpx;
          font-family: Inter-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 26rpx;
        }
        .text_4 {
          width: 158rpx;
          height: 74rpx;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 50rpx;
          font-family: Source Han Sans CN-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 50rpx;
          margin-top: 8rpx;
        }
      }
      .image_2 {
        width: 138rpx;
        height: 138rpx;
        margin: 32rpx 48rpx 0 322rpx;
      }
      .group_5 {
        background-color: rgba(21, 159, 133, 1);
        border-radius: 10px 10px 0px 0px;
        position: absolute;
        left: -2rpx;
        top: 182rpx;
        width: 702rpx;
        height: 138rpx;
        .image-text_1 {
          width: 96rpx;
          height: 82rpx;
          margin: 32rpx 0 0 70rpx;
          .group_6 {
            background-color: rgba(255, 255, 255, 1);
            width: 44rpx;
            height: 44rpx;
            margin-left: 26rpx;
          }
          .text-group_2 {
            width: 96rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 14rpx;
          }
        }
        .box_1 {
          width: 96rpx;
          height: 88rpx;
          margin: 26rpx 0 0 138rpx;
          .image-wrapper_1 {
            height: 48rpx;
            margin-left: 24rpx;
            width: 48rpx;
            .label_1 {
              width: 44rpx;
              height: 46rpx;
              margin: 2rpx 0 0 2rpx;
            }
          }
          .text_5 {
            width: 96rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 16rpx;
          }
        }
        .box_2 {
          width: 96rpx;
          height: 88rpx;
          margin: 26rpx 68rpx 0 138rpx;
          .image-wrapper_2 {
            height: 48rpx;
            margin-left: 24rpx;
            width: 48rpx;
            .label_2 {
              width: 46rpx;
              height: 42rpx;
              margin: 4rpx 0 0 2rpx;
            }
          }
          .text_6 {
            width: 96rpx;
            height: 24rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24rpx;
            font-family: Pinyon Script-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24rpx;
            margin-top: 16rpx;
          }
        }
      }
    }
  }
  .group_7 {
    position: relative;
    width: 750rpx;
    height: 1270rpx;
    margin-bottom: 2rpx;
    .text_7 {
      width: 210rpx;
      height: 42rpx;
      overflow-wrap: break-word;
      color: rgba(51, 51, 51, 1);
      font-size: 30rpx;
      font-family: PingFang SC-Medium;
      font-weight: 500;
      text-align: center;
      white-space: nowrap;
      line-height: 30rpx;
      margin: 42rpx 0 0 26rpx;
    }
    .text_8 {
      width: 200rpx;
      height: 48rpx;
      overflow-wrap: break-word;
      color: rgba(153, 153, 153, 1);
      font-size: 40rpx;
      font-family: Inter-Regular;
      font-weight: NaN;
      text-align: center;
      white-space: nowrap;
      line-height: 40rpx;
      margin: 50rpx 0 0 26rpx;
    }
    .text_9 {
      width: 210rpx;
      height: 42rpx;
      overflow-wrap: break-word;
      color: rgba(51, 51, 51, 1);
      font-size: 30rpx;
      font-family: PingFang SC-Medium;
      font-weight: 500;
      text-align: center;
      white-space: nowrap;
      line-height: 30rpx;
      margin: 58rpx 0 0 26rpx;
    }
    .box_3 {
      width: 664rpx;
      height: 114rpx;
      margin: 32rpx 0 0 24rpx;
      .box_4 {
        background-color: rgba(216, 244, 239, 1);
        border-radius: 5px;
        width: 216rpx;
        height: 114rpx;
        border: 1px solid rgba(11, 206, 148, 1);
        .text-group_3 {
          width: 138rpx;
          height: 84rpx;
          margin: 16rpx 0 0 40rpx;
          .text-wrapper_1 {
            width: 96rpx;
            height: 44rpx;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: Inter-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 36rpx;
            margin-left: 20rpx;
            .text_10 {
              width: 96rpx;
              height: 44rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 36rpx;
              font-family: Inter-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 36rpx;
            }
            .text_11 {
              width: 96rpx;
              height: 44rpx;
              overflow-wrap: break-word;
              color: rgba(11, 206, 148, 1);
              font-size: 28rpx;
              font-family: Inter-Regular;
              font-weight: NaN;
              text-align: center;
              white-space: nowrap;
              line-height: 28rpx;
            }
          }
          .text_12 {
            width: 138rpx;
            height: 36rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 26rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            white-space: nowrap;
            line-height: 26rpx;
            margin-top: 4rpx;
          }
        }
      }
      .text_13 {
        width: 170rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
        margin: 64rpx 0 0 50rpx;
      }
      .text-wrapper_2 {
        width: 96rpx;
        height: 50rpx;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 36rpx;
        margin: 16rpx 0 0 -132rpx;
        .text_14 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 36rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 36rpx;
        }
        .text_15 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
        }
      }
      .text_16 {
        width: 138rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
        margin: 64rpx 0 0 126rpx;
      }
      .text-wrapper_3 {
        width: 96rpx;
        height: 50rpx;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 36rpx;
        margin: 16rpx 20rpx 0 -116rpx;
        .text_17 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 36rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 36rpx;
        }
        .text_18 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
        }
      }
    }
    .box_5 {
      width: 624rpx;
      height: 84rpx;
      margin: 40rpx 0 0 64rpx;
      .text_19 {
        width: 138rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
        margin-top: 48rpx;
      }
      .text-wrapper_4 {
        width: 96rpx;
        height: 50rpx;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 36rpx;
        margin-left: -118rpx;
        .text_20 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 36rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 36rpx;
        }
        .text_21 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
        }
      }
      .text_22 {
        width: 138rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
        margin: 48rpx 0 0 128rpx;
      }
      .text-wrapper_5 {
        width: 96rpx;
        height: 50rpx;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 36rpx;
        margin-left: -118rpx;
        .text_23 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 36rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 36rpx;
        }
        .text_24 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
        }
      }
      .text_25 {
        width: 138rpx;
        height: 36rpx;
        overflow-wrap: break-word;
        color: rgba(153, 153, 153, 1);
        font-size: 26rpx;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 26rpx;
        margin: 48rpx 0 0 126rpx;
      }
      .text-wrapper_6 {
        width: 96rpx;
        height: 50rpx;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Regular;
        font-weight: NaN;
        text-align: center;
        white-space: nowrap;
        line-height: 36rpx;
        margin: 0 20rpx 0 -116rpx;
        .text_26 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 36rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 36rpx;
        }
        .text_27 {
          width: 96rpx;
          height: 50rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 28rpx;
        }
      }
    }
    .box_6 {
      background-color: rgba(247, 247, 247, 1);
      width: 750rpx;
      height: 20rpx;
      margin-top: 56rpx;
    }
    .box_7 {
      width: 700rpx;
      height: 28rpx;
      margin: 38rpx 0 0 24rpx;
      .text_28 {
        width: 240rpx;
        height: 26rpx;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 30rpx;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 30rpx;
      }
      .image-text_2 {
        width: 108rpx;
        height: 28rpx;
        .text-group_4 {
          width: 84rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(153, 153, 153, 1);
          font-size: 28rpx;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 28rpx;
        }
        .thumbnail_5 {
          width: 12rpx;
          height: 20rpx;
          margin-top: 4rpx;
        }
      }
    }
    .box_8 {
      width: 702rpx;
      height: 52rpx;
      margin: 48rpx 0 0 26rpx;
      .image-text_3 {
        width: 174rpx;
        height: 52rpx;
        .label_3 {
          width: 52rpx;
          height: 52rpx;
        }
        .text-group_5 {
          width: 112rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          color: rgba(51, 51, 51, 1);
          font-size: 28rpx;
          font-family: Pinyon Script-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 28rpx;
          margin-top: 12rpx;
        }
      }
      .thumbnail_6 {
        width: 40rpx;
        height: 40rpx;
        margin-top: 6rpx;
      }
    }
    .box_9 {
      width: 702rpx;
      height: 52rpx;
      margin: 30rpx 0 0 26rpx;
      .label_4 {
        width: 52rpx;
        height: 52rpx;
      }
      .text_29 {
        width: 140rpx;
        height: 28rpx;
        overflow-wrap: break-word;
        color: rgba(51, 51, 51, 1);
        font-size: 28rpx;
        font-family: Pinyon Script-Regular;
        font-weight: NaN;
        text-align: left;
        white-space: nowrap;
        line-height: 28rpx;
        margin: 12rpx 0 0 10rpx;
      }
      .thumbnail_7 {
        width: 40rpx;
        height: 40rpx;
        margin: 6rpx 0 0 460rpx;
      }
    }
    .paragraph_1 {
      width: 644rpx;
      height: 96rpx;
      overflow-wrap: break-word;
      color: rgba(153, 153, 153, 1);
      font-size: 26rpx;
      font-family: PingFang SC-Regular;
      font-weight: NaN;
      text-align: left;
      line-height: 32rpx;
      margin: 22rpx 0 0 26rpx;
    }
    .box_10 {
      background-color: rgba(247, 247, 247, 1);
      height: 258rpx;
      width: 750rpx;
      margin: 16rpx 0 2rpx 0;
      .group_8 {
        background-color: rgba(255, 255, 255, 1);
        width: 750rpx;
        height: 134rpx;
        margin-top: 124rpx;
        .text-wrapper_7 {
          width: 160rpx;
          height: 28rpx;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 28rpx;
          margin: 54rpx 0 0 24rpx;
          .text_30 {
            width: 160rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
          }
          .text_31 {
            width: 160rpx;
            height: 28rpx;
            overflow-wrap: break-word;
            color: rgba(220, 78, 76, 1);
            font-size: 28rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 28rpx;
          }
        }
        .text-wrapper_8 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 90rpx;
          width: 284rpx;
          margin: 22rpx 24rpx 0 0;
          .text_32 {
            width: 60rpx;
            height: 30rpx;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 30rpx;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 30rpx;
            margin: 30rpx 0 0 112rpx;
          }
        }
      }
    }
    .text-wrapper_9 {
      background-color: rgba(11, 206, 148, 1);
      border-radius: 10px;
      height: 88rpx;
      width: 702rpx;
      position: absolute;
      left: 24rpx;
      top: 1282rpx;
      .text_33 {
        width: 128rpx;
        height: 48rpx;
        overflow-wrap: break-word;
        color: rgba(255, 255, 255, 1);
        font-size: 32rpx;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: center;
        white-space: nowrap;
        line-height: 48rpx;
        margin: 20rpx 0 0 288rpx;
      }
    }
    .box_11 {
      border-radius: 5px;
      position: absolute;
      left: 24rpx;
      top: 452rpx;
      width: 216rpx;
      height: 114rpx;
      border: 1px solid rgba(221, 221, 221, 1);
    }
    .box_12 {
      border-radius: 5px;
      position: absolute;
      left: 268rpx;
      top: 314rpx;
      width: 216rpx;
      height: 114rpx;
      border: 1px solid rgba(221, 221, 221, 1);
    }
    .box_13 {
      border-radius: 5px;
      position: absolute;
      left: 268rpx;
      top: 452rpx;
      width: 216rpx;
      height: 114rpx;
      border: 1px solid rgba(221, 221, 221, 1);
    }
    .box_14 {
      border-radius: 5px;
      position: absolute;
      left: 512rpx;
      top: 314rpx;
      width: 216rpx;
      height: 114rpx;
      border: 1px solid rgba(221, 221, 221, 1);
    }
    .box_15 {
      border-radius: 5px;
      position: absolute;
      left: 512rpx;
      top: 452rpx;
      width: 216rpx;
      height: 114rpx;
      border: 1px solid rgba(221, 221, 221, 1);
    }
  }
}
