.page {
  background-color: rgba(11, 11, 11, 1);
  position: relative;
  width: 100vw;
  height: 285.87vw;
  overflow: hidden;
  .box_1 {
    background-color: rgba(43, 47, 54, 1);
    width: 100vw;
    height: 167.47vw;
    margin-top: 177.87vw;
  }
  .box_2 {
    position: absolute;
    left: -50.4vw;
    top: -46.66vw;
    width: 183.2vw;
    height: 263.2vw;
    background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG1b24b8fb2f4951617799f0f75436778b.png)
      100% no-repeat;
    background-size: 100% 100%;
    .block_1 {
      background-color: rgba(255, 255, 255, 1);
      height: 40.8vw;
      width: 100.27vw;
      margin: 208vw 0 0 50.13vw;
      .text-wrapper_1 {
        width: 17.07vw;
        height: 5.07vw;
        margin: 4.8vw 0 0 7.46vw;
        .text_1 {
          width: 17.07vw;
          height: 5.07vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 4.26vw;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 32vw;
        }
      }
      .section_1 {
        width: 81.87vw;
        height: 13.07vw;
        margin: 3.46vw 0 0 9.6vw;
        .section_2 {
          background-image: linear-gradient(
            136deg,
            rgba(246, 200, 101, 1) 0,
            rgba(240, 177, 76, 1) 100%
          );
          border-radius: 50%;
          width: 13.07vw;
          height: 13.07vw;
        }
        .section_3 {
          background-image: linear-gradient(
            148deg,
            rgba(239, 144, 133, 1) 0,
            rgba(238, 124, 104, 1) 100%
          );
          border-radius: 50%;
          width: 13.07vw;
          height: 13.07vw;
          margin-left: 9.87vw;
        }
        .section_4 {
          background-image: linear-gradient(
            141deg,
            rgba(112, 193, 126, 1) 0,
            rgba(78, 177, 111, 1) 100%
          );
          border-radius: 50%;
          width: 13.07vw;
          height: 13.07vw;
          margin-left: 9.87vw;
        }
        .section_5 {
          background-image: linear-gradient(
            180deg,
            rgba(255, 185, 186, 1) 0,
            rgba(248, 161, 162, 1) 100%
          );
          border-radius: 50%;
          width: 13.07vw;
          height: 13.07vw;
          margin-left: 9.87vw;
        }
      }
      .section_6 {
        width: 83.74vw;
        height: 9.34vw;
        margin: 0.8vw 0 4.26vw 8.53vw;
        .text-wrapper_2 {
          width: 15.74vw;
          height: 9.34vw;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 35.74vw;
          .paragraph_1 {
            width: 15.74vw;
            height: 9.34vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 35.74vw;
          }
          .text_2 {
            width: 15.74vw;
            height: 9.34vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 35.74vw;
          }
        }
        .text-wrapper_3 {
          width: 16vw;
          height: 9.34vw;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 35.74vw;
          margin-left: 5.6vw;
          .paragraph_2 {
            width: 16vw;
            height: 9.34vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 35.74vw;
          }
          .text_3 {
            width: 16vw;
            height: 9.34vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 35.74vw;
          }
        }
        .text-wrapper_4 {
          width: 14.94vw;
          height: 9.34vw;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 35.74vw;
          margin-left: 9.87vw;
          .paragraph_3 {
            width: 14.94vw;
            height: 9.34vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 35.74vw;
          }
          .text_4 {
            width: 14.94vw;
            height: 9.34vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 35.74vw;
          }
        }
        .text-wrapper_5 {
          width: 14.94vw;
          height: 9.34vw;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 35.74vw;
          margin-left: 6.67vw;
          .paragraph_4 {
            width: 14.94vw;
            height: 9.34vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 35.74vw;
          }
          .text_5 {
            width: 14.94vw;
            height: 9.34vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 35.74vw;
          }
        }
      }
    }
    .block_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 20px 20px 0px 0px;
      width: 100vw;
      height: 90.14vw;
      margin: 118.13vw 32.8vw 0 -100vw;
      .section_7 {
        width: 91.2vw;
        height: 34.94vw;
        margin: 15.46vw 0 0 4.26vw;
        .group_1 {
          background-color: rgba(255, 244, 233, 1);
          border-radius: 10px;
          position: relative;
          width: 29.34vw;
          height: 34.94vw;
          border: 2px solid rgba(216, 177, 145, 1);
          .text_6 {
            width: 14.94vw;
            height: 6.4vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 6.4vw;
            margin: 2.93vw 0 0 7.2vw;
          }
          .text-wrapper_6 {
            width: 16vw;
            height: 6.4vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 6.4vw;
            margin: 0.8vw 0 0 6.66vw;
            .text_7 {
              width: 16vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 4.26vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 6.4vw;
            }
            .text_8 {
              width: 16vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 6.4vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 6.4vw;
            }
          }
          .text_9 {
            width: 9.07vw;
            height: 6.4vw;
            overflow-wrap: break-word;
            color: rgba(174, 177, 183, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            text-decoration: line-through;
            font-weight: NaN;
            text-align: left;
            line-height: 6.4vw;
            margin: 1.33vw 0 0 10.13vw;
          }
          .group_2 {
            background-image: linear-gradient(
              90deg,
              rgba(240, 193, 170, 1) 0,
              rgba(215, 176, 143, 1) 100%
            );
            border-radius: 10px;
            width: 20.27vw;
            height: 5.6vw;
            margin: 1.33vw 0 3.73vw 4.53vw;
          }
          .text_10 {
            position: absolute;
            left: 8vw;
            top: 25.07vw;
            width: 13.6vw;
            height: 6.4vw;
            overflow-wrap: break-word;
            color: rgba(151, 96, 58, 1);
            font-size: 3.2vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            line-height: 6.4vw;
          }
        }
        .group_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 29.34vw;
          height: 34.94vw;
          border: 2px solid rgba(228, 228, 228, 1);
          margin-left: 1.6vw;
          .text-group_1 {
            width: 18.67vw;
            height: 21.34vw;
            margin: 2.93vw 0 0 5.33vw;
            .text_11 {
              width: 18.67vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 3.73vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 6.4vw;
            }
            .text-wrapper_7 {
              width: 16vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 6.4vw;
              margin: 0.8vw 0 0 1.33vw;
              .text_12 {
                width: 16vw;
                height: 6.4vw;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 4.26vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 6.4vw;
              }
              .text_13 {
                width: 16vw;
                height: 6.4vw;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 6.4vw;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 6.4vw;
              }
            }
            .text_14 {
              width: 9.07vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(174, 177, 183, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              text-decoration: line-through;
              font-weight: NaN;
              text-align: left;
              line-height: 6.4vw;
              margin: 1.33vw 0 0 4.8vw;
            }
          }
          .text-wrapper_8 {
            background-color: rgba(219, 219, 219, 1);
            border-radius: 10px;
            height: 5.6vw;
            width: 20.27vw;
            margin: 0.8vw 0 4.26vw 4.53vw;
            .text_15 {
              width: 13.6vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(148, 148, 148, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              line-height: 6.4vw;
              margin-left: 3.47vw;
            }
          }
        }
        .group_4 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 29.34vw;
          height: 34.94vw;
          border: 2px solid rgba(228, 228, 228, 1);
          margin-left: 1.6vw;
          .text-group_2 {
            width: 16.54vw;
            height: 21.34vw;
            margin: 2.93vw 0 0 6.4vw;
            .text_16 {
              width: 14.94vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 3.73vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 6.4vw;
              margin-left: 0.8vw;
            }
            .text-wrapper_9 {
              width: 16.54vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 6.4vw;
              margin-top: 0.8vw;
              .text_17 {
                width: 16.54vw;
                height: 6.4vw;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 4.8vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 6.4vw;
              }
              .text_18 {
                width: 16.54vw;
                height: 6.4vw;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 6.4vw;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 6.4vw;
              }
            }
            .text_19 {
              width: 9.07vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(174, 177, 183, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              text-decoration: line-through;
              font-weight: NaN;
              text-align: left;
              line-height: 6.4vw;
              margin: 1.33vw 0 0 3.73vw;
            }
          }
          .text-wrapper_10 {
            background-color: rgba(219, 219, 219, 1);
            border-radius: 10px;
            height: 5.6vw;
            width: 20.27vw;
            margin: 0.8vw 0 4.26vw 4.53vw;
            .text_20 {
              width: 13.6vw;
              height: 6.4vw;
              overflow-wrap: break-word;
              color: rgba(148, 148, 148, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              line-height: 6.4vw;
              margin-left: 3.47vw;
            }
          }
        }
      }
      .section_8 {
        background-image: linear-gradient(
          90deg,
          rgba(43, 47, 54, 1) 0,
          rgba(101, 95, 92, 1) 100%
        );
        border-radius: 100px;
        width: 92.8vw;
        height: 13.34vw;
        margin: 8.8vw 0 0 3.46vw;
        .text-wrapper_11 {
          width: 16vw;
          height: 6.4vw;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 6.4vw;
          margin: 3.46vw 0 0 27.73vw;
          .text_21 {
            width: 16vw;
            height: 6.4vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 4.26vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 6.4vw;
          }
          .text_22 {
            width: 16vw;
            height: 6.4vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 6.4vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 6.4vw;
          }
        }
        .text_23 {
          width: 19.2vw;
          height: 6.4vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 4.8vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 6.4vw;
          margin: 3.46vw 27.73vw 0 0;
        }
      }
      .section_9 {
        width: 58.14vw;
        height: 6.4vw;
        margin: 3.73vw 0 0 20vw;
        .group_5 {
          border-radius: 50%;
          width: 4vw;
          height: 4vw;
          border: 1px solid rgba(153, 153, 153, 1);
          margin-top: 1.07vw;
        }
        .text-wrapper_12 {
          width: 52.27vw;
          height: 6.4vw;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 6.4vw;
          .text_24 {
            width: 52.27vw;
            height: 6.4vw;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 6.4vw;
          }
          .text_25 {
            width: 52.27vw;
            height: 6.4vw;
            overflow-wrap: break-word;
            color: rgba(215, 175, 142, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 6.4vw;
          }
        }
      }
      .section_10 {
        background-color: rgba(245, 245, 245, 1);
        width: 100vw;
        height: 2.67vw;
        margin: 4vw 0 0.8vw 0;
      }
    }
    .block_3 {
      position: absolute;
      left: 0;
      top: 0;
      width: 183.2vw;
      height: 118.4vw;
      .group_6 {
        width: 91.74vw;
        height: 5.07vw;
        margin: 48.53vw 0 0 54.66vw;
        .text_26 {
          width: 8.54vw;
          height: 4.8vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 3.2vw;
          font-family: Inter-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 40vw;
        }
        .thumbnail_1 {
          width: 4.8vw;
          height: 4.8vw;
          margin-left: 66.94vw;
        }
        .thumbnail_2 {
          width: 4.8vw;
          height: 4.8vw;
          margin-left: 0.8vw;
        }
        .thumbnail_3 {
          width: 5.07vw;
          height: 5.07vw;
          margin-left: 0.8vw;
        }
      }
      .group_7 {
        width: 38.4vw;
        height: 38.4vw;
        margin: 9.86vw 0 16.53vw 50.66vw;
        .section_11 {
          position: relative;
          width: 38.4vw;
          height: 38.4vw;
          background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG48515ac40375b7c83a6436611c40671b.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image-wrapper_1 {
            height: 38.4vw;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG65f5b26c5fff306b2173835ab441c322.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 38.4vw;
            position: absolute;
            left: 30.14vw;
            top: 0;
            .image_1 {
              width: 38.4vw;
              height: 38.4vw;
            }
          }
          .thumbnail_4 {
            position: absolute;
            left: 4.54vw;
            top: -3.2vw;
            width: 2.4vw;
            height: 4.54vw;
          }
        }
      }
      .text-wrapper_13 {
        position: absolute;
        left: 60.54vw;
        top: 59.47vw;
        width: 17.07vw;
        height: 5.87vw;
        .text_27 {
          width: 17.07vw;
          height: 5.87vw;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 4.26vw;
          font-family: Inter-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 5.87vw;
        }
      }
      .group_8 {
        box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 1);
        background-image: linear-gradient(
          154deg,
          rgba(255, 250, 240, 1) 0,
          rgba(206, 158, 120, 1) 100%
        );
        border-radius: 20px;
        height: 40.27vw;
        width: 91.47vw;
        position: absolute;
        left: 54.67vw;
        top: 72vw;
        .box_3 {
          box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 1);
          background-image: linear-gradient(
            146deg,
            rgba(255, 222, 157, 1) 0,
            rgba(206, 158, 120, 1) 100%
          );
          border-radius: 20px;
          height: 40.27vw;
          width: 91.47vw;
          position: relative;
          .group_9 {
            width: 17.6vw;
            height: 18.14vw;
            margin: 0.26vw 0 0 73.86vw;
            .box_4 {
              width: 17.6vw;
              height: 18.14vw;
              background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGb293715fa2fe32a97708a7696cac578f.png)
                100% no-repeat;
              background-size: 100% 100%;
            }
          }
          .text-wrapper_14 {
            width: 45.6vw;
            height: 3.2vw;
            margin: 6.66vw 0 12vw 4.53vw;
            .text_28 {
              width: 45.6vw;
              height: 3.2vw;
              overflow-wrap: break-word;
              color: rgba(62, 29, 4, 1);
              font-size: 5.06vw;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2vw;
            }
          }
          .group_10 {
            background-image: linear-gradient(
              180deg,
              rgba(255, 240, 223, 1) 0,
              rgba(255, 245, 239, 0) 100%
            );
            position: absolute;
            left: 3.47vw;
            top: -6.93vw;
            width: 19.47vw;
            height: 59.47vw;
          }
          .group_11 {
            background-image: linear-gradient(
              180deg,
              rgba(255, 245, 239, 1) 0,
              rgba(255, 245, 239, 0) 100%
            );
            position: absolute;
            left: 14.94vw;
            top: -32.8vw;
            width: 19.47vw;
            height: 75.47vw;
          }
          .group_12 {
            height: 30.4vw;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG577b88c99c41fa54b92d33e4e334f86b.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 33.34vw;
            position: absolute;
            left: 58.14vw;
            top: 5.07vw;
            .text-wrapper_15 {
              height: 8vw;
              background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG45961fda21afd8e4b2646cfb27026d11.png)
                100% no-repeat;
              background-size: 100% 100%;
              width: 24vw;
              margin: 17.6vw 0 0 5.6vw;
              .text_29 {
                width: 14.94vw;
                height: 3.74vw;
                overflow-wrap: break-word;
                color: rgba(92, 92, 92, 1);
                font-size: 3.73vw;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 3.74vw;
                margin: 2.13vw 0 0 4.53vw;
              }
            }
            .group_13 {
              background-color: rgba(232, 191, 155, 0.4);
              height: 9.34vw;
              width: 91.47vw;
              position: absolute;
              left: -58.13vw;
              top: 0.8vw;
              .text_30 {
                background-image: linear-gradient(
                  146deg,
                  rgba(213, 172, 135, 1) 0,
                  rgba(185, 124, 72, 1) 100%
                );
                width: 26.67vw;
                height: 5.34vw;
                overflow-wrap: break-word;
                color: ;
                font-size: 5.33vw;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 5.34vw;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin: 2.4vw 0 0 21.06vw;
              }
              .group_14 {
                position: absolute;
                left: 75.47vw;
                top: -2.13vw;
                width: 4vw;
                height: 5.87vw;
                background: url(/static/lanhu_huiyuanjiemian_1/6456d02b8b73459b9038769094e48df1_mergeImage.png)
                  100% no-repeat;
                background-size: 100% 100%;
              }
              .group_15 {
                position: absolute;
                left: 82.67vw;
                top: -2.13vw;
                width: 4vw;
                height: 5.87vw;
                background: url(/static/lanhu_huiyuanjiemian_1/b77255305c91446bbf19b6699904f13e_mergeImage.png)
                  100% no-repeat;
                background-size: 100% 100%;
              }
            }
          }
          .image-wrapper_2 {
            background-image: linear-gradient(
              179deg,
              rgba(255, 234, 193, 1) 0,
              rgba(235, 193, 162, 1) 100%
            );
            border-radius: 50%;
            height: 14.94vw;
            border: 2px gradient;
            width: 14.94vw;
            position: absolute;
            left: 3.74vw;
            top: 3.2vw;
            .label_1 {
              width: 8.54vw;
              height: 11.2vw;
              margin: 1.86vw 0 0 3.2vw;
            }
          }
        }
      }
      .group_16 {
        background-image: linear-gradient(
          179deg,
          rgba(248, 236, 197, 1) 0,
          rgba(231, 151, 93, 1) 100%
        );
        border-radius: 50%;
        height: 9.34vw;
        border: 1px gradient;
        width: 9.34vw;
        position: absolute;
        left: 129.07vw;
        top: 69.87vw;
        .block_4 {
          background-image: linear-gradient(
            138deg,
            rgba(255, 247, 234, 1) 0,
            rgba(244, 162, 98, 1) 100%
          );
          border-radius: 2px;
          height: 8.27vw;
          border: 1px gradient;
          width: 8.27vw;
          margin: 0.8vw 0 0 0.53vw;
          .section_12 {
            height: 8.27vw;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG5f34148596fd027a8f98a27ad3c7dcb3.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 8.27vw;
            .group_17 {
              box-shadow: inset 1px 0px 0px 0px rgba(0, 0, 0, 0.21);
              background-image: linear-gradient(
                143deg,
                rgba(234, 164, 110, 1) 0,
                rgba(241, 171, 118, 1) 100%
              );
              border-radius: 1px;
              height: 5.87vw;
              width: 5.87vw;
              margin: 1.06vw 0 0 1.06vw;
              .box_5 {
                height: 5.87vw;
                background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGee080a99c1f66756c342138bdf7118ce.png)
                  100% no-repeat;
                background-size: 100% 100%;
                width: 5.87vw;
                .section_13 {
                  width: 2.94vw;
                  height: 2.94vw;
                  background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGc1f566eb5fff4bce5920da1fa503760f.png)
                    100% no-repeat;
                  background-size: 100% 100%;
                  margin: 1.86vw 0 0 1.86vw;
                }
              }
            }
          }
        }
      }
      .text-wrapper_16 {
        position: absolute;
        left: 54.67vw;
        top: 122.4vw;
        width: 50.67vw;
        height: 6.4vw;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 6.4vw;
        .text_31 {
          width: 50.67vw;
          height: 6.4vw;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 4.26vw;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 6.4vw;
        }
        .text_32 {
          width: 50.67vw;
          height: 6.4vw;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 3.46vw;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 6.4vw;
        }
      }
      .image_2 {
        position: absolute;
        left: 125.6vw;
        top: 58.14vw;
        width: 23.2vw;
        height: 8.54vw;
      }
    }
    .block_5 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 50.4vw;
      top: 248.54vw;
      width: 100vw;
      height: 84vw;
      .box_6 {
        background-color: rgba(245, 245, 245, 1);
        width: 100vw;
        height: 2.67vw;
      }
      .text_33 {
        width: 17.07vw;
        height: 5.07vw;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 4.26vw;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 32vw;
        margin: 2.4vw 0 0 7.2vw;
      }
      .box_7 {
        width: 84.54vw;
        height: 11.74vw;
        margin: 5.33vw 0 0 7.73vw;
        .image-wrapper_3 {
          background-color: rgba(233, 248, 241, 1);
          border-radius: 50%;
          height: 11.74vw;
          width: 11.74vw;
          .label_2 {
            width: 6.14vw;
            height: 6.14vw;
            margin: 2.66vw 0 0 2.93vw;
          }
        }
        .text-group_3 {
          width: 36vw;
          height: 9.6vw;
          margin: 0.8vw 0 0 1.86vw;
          .text_34 {
            width: 14.94vw;
            height: 4.54vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
          }
          .text-wrapper_17 {
            width: 36vw;
            height: 3.74vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
            margin-top: 1.34vw;
            .text_35 {
              width: 36vw;
              height: 3.74vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 32vw;
            }
            .text_36 {
              width: 36vw;
              height: 3.74vw;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 32vw;
            }
          }
        }
        .text-wrapper_18 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 8vw;
          width: 18.94vw;
          margin: 1.6vw 0 0 16vw;
          .text_37 {
            width: 10.4vw;
            height: 4.27vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.46vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
            margin: 1.86vw 0 0 4.26vw;
          }
        }
      }
      .box_8 {
        width: 84.54vw;
        height: 11.74vw;
        margin: 4.26vw 0 0 8vw;
        .label_3 {
          width: 11.74vw;
          height: 11.74vw;
        }
        .text-group_4 {
          width: 36vw;
          height: 9.6vw;
          margin: 0.8vw 0 0 1.86vw;
          .text_38 {
            width: 7.47vw;
            height: 4.54vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
          }
          .text-wrapper_19 {
            width: 36vw;
            height: 3.74vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
            margin-top: 1.34vw;
            .text_39 {
              width: 36vw;
              height: 3.74vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 32vw;
            }
            .text_40 {
              width: 36vw;
              height: 3.74vw;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 32vw;
            }
          }
        }
        .text-wrapper_20 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 8vw;
          width: 18.94vw;
          margin: 1.6vw 0 0 16vw;
          .text_41 {
            width: 10.4vw;
            height: 4.27vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.46vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
            margin: 1.86vw 0 0 4.26vw;
          }
        }
      }
      .box_9 {
        width: 84.54vw;
        height: 11.74vw;
        margin: 5.06vw 0 0 8vw;
        .section_14 {
          background-color: rgba(255, 245, 240, 1);
          border-radius: 50%;
          height: 11.74vw;
          width: 11.74vw;
          .box_10 {
            background-color: rgba(255, 104, 83, 1);
            width: 6.94vw;
            height: 6.4vw;
            margin: 2.93vw 0 0 2.4vw;
          }
        }
        .text-group_5 {
          width: 36vw;
          height: 9.6vw;
          margin: 0.8vw 0 0 1.86vw;
          .text_42 {
            width: 7.47vw;
            height: 4.54vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
          }
          .text-wrapper_21 {
            width: 36vw;
            height: 3.74vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
            margin-top: 1.34vw;
            .text_43 {
              width: 36vw;
              height: 3.74vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 32vw;
            }
            .text_44 {
              width: 36vw;
              height: 3.74vw;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 32vw;
            }
          }
        }
        .text-wrapper_22 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 8vw;
          width: 18.94vw;
          margin: 1.6vw 0 0 16vw;
          .text_45 {
            width: 10.4vw;
            height: 4.27vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.46vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
            margin: 1.86vw 0 0 4.26vw;
          }
        }
      }
      .box_11 {
        width: 84.54vw;
        height: 11.74vw;
        margin: 5.33vw 0 6.93vw 8.26vw;
        .image-wrapper_4 {
          background-color: rgba(241, 251, 242, 1);
          border-radius: 50%;
          height: 11.74vw;
          width: 11.74vw;
          .label_4 {
            width: 6.4vw;
            height: 6.4vw;
            margin: 2.66vw 0 0 2.66vw;
          }
        }
        .text-group_6 {
          width: 36vw;
          height: 9.6vw;
          margin: 0.8vw 0 0 1.86vw;
          .text_46 {
            width: 14.94vw;
            height: 4.54vw;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 3.73vw;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
          }
          .text-wrapper_23 {
            width: 36vw;
            height: 3.74vw;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
            margin-top: 1.34vw;
            .text_47 {
              width: 36vw;
              height: 3.74vw;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 32vw;
            }
            .text_48 {
              width: 36vw;
              height: 3.74vw;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 3.2vw;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 32vw;
            }
          }
        }
        .text-wrapper_24 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 8vw;
          width: 18.94vw;
          margin: 1.6vw 0 0 16vw;
          .text_49 {
            width: 10.4vw;
            height: 4.27vw;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 3.46vw;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 32vw;
            margin: 1.86vw 0 0 4.26vw;
          }
        }
      }
    }
  }
}
