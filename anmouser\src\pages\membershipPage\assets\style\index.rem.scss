.page {
  background-color: rgba(11, 11, 11, 1);
  position: relative;
  width: 10rem;
  height: 28.587rem;
  overflow: hidden;
  .box_1 {
    background-color: rgba(43, 47, 54, 1);
    width: 10rem;
    height: 16.747rem;
    margin-top: 17.787rem;
  }
  .box_2 {
    position: absolute;
    left: -5.04rem;
    top: -4.666rem;
    width: 18.32rem;
    height: 26.32rem;
    background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG1b24b8fb2f4951617799f0f75436778b.png)
      100% no-repeat;
    background-size: 100% 100%;
    .block_1 {
      background-color: rgba(255, 255, 255, 1);
      height: 4.08rem;
      width: 10.027rem;
      margin: 20.8rem 0 0 5.014rem;
      .text-wrapper_1 {
        width: 1.707rem;
        height: 0.507rem;
        margin: 0.48rem 0 0 0.747rem;
        .text_1 {
          width: 1.707rem;
          height: 0.507rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.426rem;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 3.2rem;
        }
      }
      .section_1 {
        width: 8.187rem;
        height: 1.307rem;
        margin: 0.347rem 0 0 0.96rem;
        .section_2 {
          background-image: linear-gradient(
            136deg,
            rgba(246, 200, 101, 1) 0,
            rgba(240, 177, 76, 1) 100%
          );
          border-radius: 50%;
          width: 1.307rem;
          height: 1.307rem;
        }
        .section_3 {
          background-image: linear-gradient(
            148deg,
            rgba(239, 144, 133, 1) 0,
            rgba(238, 124, 104, 1) 100%
          );
          border-radius: 50%;
          width: 1.307rem;
          height: 1.307rem;
          margin-left: 0.987rem;
        }
        .section_4 {
          background-image: linear-gradient(
            141deg,
            rgba(112, 193, 126, 1) 0,
            rgba(78, 177, 111, 1) 100%
          );
          border-radius: 50%;
          width: 1.307rem;
          height: 1.307rem;
          margin-left: 0.987rem;
        }
        .section_5 {
          background-image: linear-gradient(
            180deg,
            rgba(255, 185, 186, 1) 0,
            rgba(248, 161, 162, 1) 100%
          );
          border-radius: 50%;
          width: 1.307rem;
          height: 1.307rem;
          margin-left: 0.987rem;
        }
      }
      .section_6 {
        width: 8.374rem;
        height: 0.934rem;
        margin: 0.08rem 0 0.427rem 0.854rem;
        .text-wrapper_2 {
          width: 1.574rem;
          height: 0.934rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 3.574rem;
          .paragraph_1 {
            width: 1.574rem;
            height: 0.934rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 3.574rem;
          }
          .text_2 {
            width: 1.574rem;
            height: 0.934rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 3.574rem;
          }
        }
        .text-wrapper_3 {
          width: 1.6rem;
          height: 0.934rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 3.574rem;
          margin-left: 0.56rem;
          .paragraph_2 {
            width: 1.6rem;
            height: 0.934rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 3.574rem;
          }
          .text_3 {
            width: 1.6rem;
            height: 0.934rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 3.574rem;
          }
        }
        .text-wrapper_4 {
          width: 1.494rem;
          height: 0.934rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 3.574rem;
          margin-left: 0.987rem;
          .paragraph_3 {
            width: 1.494rem;
            height: 0.934rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 3.574rem;
          }
          .text_4 {
            width: 1.494rem;
            height: 0.934rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 3.574rem;
          }
        }
        .text-wrapper_5 {
          width: 1.494rem;
          height: 0.934rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 3.574rem;
          margin-left: 0.667rem;
          .paragraph_4 {
            width: 1.494rem;
            height: 0.934rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 3.574rem;
          }
          .text_5 {
            width: 1.494rem;
            height: 0.934rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 3.574rem;
          }
        }
      }
    }
    .block_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 20px 20px 0px 0px;
      width: 10rem;
      height: 9.014rem;
      margin: 11.814rem 3.28rem 0 -10rem;
      .section_7 {
        width: 9.12rem;
        height: 3.494rem;
        margin: 1.547rem 0 0 0.427rem;
        .group_1 {
          background-color: rgba(255, 244, 233, 1);
          border-radius: 10px;
          position: relative;
          width: 2.934rem;
          height: 3.494rem;
          border: 2px solid rgba(216, 177, 145, 1);
          .text_6 {
            width: 1.494rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
            margin: 0.294rem 0 0 0.72rem;
          }
          .text-wrapper_6 {
            width: 1.6rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
            margin: 0.08rem 0 0 0.667rem;
            .text_7 {
              width: 1.6rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.426rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
            }
            .text_8 {
              width: 1.6rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.64rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
            }
          }
          .text_9 {
            width: 0.907rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(174, 177, 183, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            text-decoration: line-through;
            font-weight: NaN;
            text-align: left;
            line-height: 0.64rem;
            margin: 0.134rem 0 0 1.014rem;
          }
          .group_2 {
            background-image: linear-gradient(
              90deg,
              rgba(240, 193, 170, 1) 0,
              rgba(215, 176, 143, 1) 100%
            );
            border-radius: 10px;
            width: 2.027rem;
            height: 0.56rem;
            margin: 0.134rem 0 0.374rem 0.454rem;
          }
          .text_10 {
            position: absolute;
            left: 0.8rem;
            top: 2.507rem;
            width: 1.36rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(151, 96, 58, 1);
            font-size: 0.32rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            line-height: 0.64rem;
          }
        }
        .group_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 2.934rem;
          height: 3.494rem;
          border: 2px solid rgba(228, 228, 228, 1);
          margin-left: 0.16rem;
          .text-group_1 {
            width: 1.867rem;
            height: 2.134rem;
            margin: 0.294rem 0 0 0.534rem;
            .text_11 {
              width: 1.867rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.373rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
            }
            .text-wrapper_7 {
              width: 1.6rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
              margin: 0.08rem 0 0 0.134rem;
              .text_12 {
                width: 1.6rem;
                height: 0.64rem;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 0.426rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 0.64rem;
              }
              .text_13 {
                width: 1.6rem;
                height: 0.64rem;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 0.64rem;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.64rem;
              }
            }
            .text_14 {
              width: 0.907rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(174, 177, 183, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              text-decoration: line-through;
              font-weight: NaN;
              text-align: left;
              line-height: 0.64rem;
              margin: 0.134rem 0 0 0.48rem;
            }
          }
          .text-wrapper_8 {
            background-color: rgba(219, 219, 219, 1);
            border-radius: 10px;
            height: 0.56rem;
            width: 2.027rem;
            margin: 0.08rem 0 0.427rem 0.454rem;
            .text_15 {
              width: 1.36rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(148, 148, 148, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              line-height: 0.64rem;
              margin-left: 0.347rem;
            }
          }
        }
        .group_4 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 2.934rem;
          height: 3.494rem;
          border: 2px solid rgba(228, 228, 228, 1);
          margin-left: 0.16rem;
          .text-group_2 {
            width: 1.654rem;
            height: 2.134rem;
            margin: 0.294rem 0 0 0.64rem;
            .text_16 {
              width: 1.494rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 0.373rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
              margin-left: 0.08rem;
            }
            .text-wrapper_9 {
              width: 1.654rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 0.64rem;
              margin-top: 0.08rem;
              .text_17 {
                width: 1.654rem;
                height: 0.64rem;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 0.48rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 0.64rem;
              }
              .text_18 {
                width: 1.654rem;
                height: 0.64rem;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 0.64rem;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.64rem;
              }
            }
            .text_19 {
              width: 0.907rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(174, 177, 183, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              text-decoration: line-through;
              font-weight: NaN;
              text-align: left;
              line-height: 0.64rem;
              margin: 0.134rem 0 0 0.374rem;
            }
          }
          .text-wrapper_10 {
            background-color: rgba(219, 219, 219, 1);
            border-radius: 10px;
            height: 0.56rem;
            width: 2.027rem;
            margin: 0.08rem 0 0.427rem 0.454rem;
            .text_20 {
              width: 1.36rem;
              height: 0.64rem;
              overflow-wrap: break-word;
              color: rgba(148, 148, 148, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              line-height: 0.64rem;
              margin-left: 0.347rem;
            }
          }
        }
      }
      .section_8 {
        background-image: linear-gradient(
          90deg,
          rgba(43, 47, 54, 1) 0,
          rgba(101, 95, 92, 1) 100%
        );
        border-radius: 100px;
        width: 9.28rem;
        height: 1.334rem;
        margin: 0.88rem 0 0 0.347rem;
        .text-wrapper_11 {
          width: 1.6rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.347rem 0 0 2.774rem;
          .text_21 {
            width: 1.6rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.426rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_22 {
            width: 1.6rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.64rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
        }
        .text_23 {
          width: 1.92rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.48rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.64rem;
          margin: 0.347rem 2.774rem 0 0;
        }
      }
      .section_9 {
        width: 5.814rem;
        height: 0.64rem;
        margin: 0.374rem 0 0 2rem;
        .group_5 {
          border-radius: 50%;
          width: 0.4rem;
          height: 0.4rem;
          border: 1px solid rgba(153, 153, 153, 1);
          margin-top: 0.107rem;
        }
        .text-wrapper_12 {
          width: 5.227rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.64rem;
          .text_24 {
            width: 5.227rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
          .text_25 {
            width: 5.227rem;
            height: 0.64rem;
            overflow-wrap: break-word;
            color: rgba(215, 175, 142, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 0.64rem;
          }
        }
      }
      .section_10 {
        background-color: rgba(245, 245, 245, 1);
        width: 10rem;
        height: 0.267rem;
        margin: 0.4rem 0 0.08rem 0;
      }
    }
    .block_3 {
      position: absolute;
      left: 0;
      top: 0;
      width: 18.32rem;
      height: 11.84rem;
      .group_6 {
        width: 9.174rem;
        height: 0.507rem;
        margin: 4.854rem 0 0 5.467rem;
        .text_26 {
          width: 0.854rem;
          height: 0.48rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.32rem;
          font-family: Inter-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 4rem;
        }
        .thumbnail_1 {
          width: 0.48rem;
          height: 0.48rem;
          margin-left: 6.694rem;
        }
        .thumbnail_2 {
          width: 0.48rem;
          height: 0.48rem;
          margin-left: 0.08rem;
        }
        .thumbnail_3 {
          width: 0.507rem;
          height: 0.507rem;
          margin-left: 0.08rem;
        }
      }
      .group_7 {
        width: 3.84rem;
        height: 3.84rem;
        margin: 0.987rem 0 1.654rem 5.067rem;
        .section_11 {
          position: relative;
          width: 3.84rem;
          height: 3.84rem;
          background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG48515ac40375b7c83a6436611c40671b.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image-wrapper_1 {
            height: 3.84rem;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG65f5b26c5fff306b2173835ab441c322.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 3.84rem;
            position: absolute;
            left: 3.014rem;
            top: 0;
            .image_1 {
              width: 3.84rem;
              height: 3.84rem;
            }
          }
          .thumbnail_4 {
            position: absolute;
            left: 0.454rem;
            top: -0.32rem;
            width: 0.24rem;
            height: 0.454rem;
          }
        }
      }
      .text-wrapper_13 {
        position: absolute;
        left: 6.054rem;
        top: 5.947rem;
        width: 1.707rem;
        height: 0.587rem;
        .text_27 {
          width: 1.707rem;
          height: 0.587rem;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 0.426rem;
          font-family: Inter-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 0.587rem;
        }
      }
      .group_8 {
        box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 1);
        background-image: linear-gradient(
          154deg,
          rgba(255, 250, 240, 1) 0,
          rgba(206, 158, 120, 1) 100%
        );
        border-radius: 20px;
        height: 4.027rem;
        width: 9.147rem;
        position: absolute;
        left: 5.467rem;
        top: 7.2rem;
        .box_3 {
          box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 1);
          background-image: linear-gradient(
            146deg,
            rgba(255, 222, 157, 1) 0,
            rgba(206, 158, 120, 1) 100%
          );
          border-radius: 20px;
          height: 4.027rem;
          width: 9.147rem;
          position: relative;
          .group_9 {
            width: 1.76rem;
            height: 1.814rem;
            margin: 0.027rem 0 0 7.387rem;
            .box_4 {
              width: 1.76rem;
              height: 1.814rem;
              background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGb293715fa2fe32a97708a7696cac578f.png)
                100% no-repeat;
              background-size: 100% 100%;
            }
          }
          .text-wrapper_14 {
            width: 4.56rem;
            height: 0.32rem;
            margin: 0.667rem 0 1.2rem 0.454rem;
            .text_28 {
              width: 4.56rem;
              height: 0.32rem;
              overflow-wrap: break-word;
              color: rgba(62, 29, 4, 1);
              font-size: 0.506rem;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 0.32rem;
            }
          }
          .group_10 {
            background-image: linear-gradient(
              180deg,
              rgba(255, 240, 223, 1) 0,
              rgba(255, 245, 239, 0) 100%
            );
            position: absolute;
            left: 0.347rem;
            top: -0.693rem;
            width: 1.947rem;
            height: 5.947rem;
          }
          .group_11 {
            background-image: linear-gradient(
              180deg,
              rgba(255, 245, 239, 1) 0,
              rgba(255, 245, 239, 0) 100%
            );
            position: absolute;
            left: 1.494rem;
            top: -3.28rem;
            width: 1.947rem;
            height: 7.547rem;
          }
          .group_12 {
            height: 3.04rem;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG577b88c99c41fa54b92d33e4e334f86b.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 3.334rem;
            position: absolute;
            left: 5.814rem;
            top: 0.507rem;
            .text-wrapper_15 {
              height: 0.8rem;
              background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG45961fda21afd8e4b2646cfb27026d11.png)
                100% no-repeat;
              background-size: 100% 100%;
              width: 2.4rem;
              margin: 1.76rem 0 0 0.56rem;
              .text_29 {
                width: 1.494rem;
                height: 0.374rem;
                overflow-wrap: break-word;
                color: rgba(92, 92, 92, 1);
                font-size: 0.373rem;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 0.374rem;
                margin: 0.214rem 0 0 0.454rem;
              }
            }
            .group_13 {
              background-color: rgba(232, 191, 155, 0.4);
              height: 0.934rem;
              width: 9.147rem;
              position: absolute;
              left: -5.813rem;
              top: 0.08rem;
              .text_30 {
                background-image: linear-gradient(
                  146deg,
                  rgba(213, 172, 135, 1) 0,
                  rgba(185, 124, 72, 1) 100%
                );
                width: 2.667rem;
                height: 0.534rem;
                overflow-wrap: break-word;
                color: ;
                font-size: 0.533rem;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 0.534rem;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin: 0.24rem 0 0 2.107rem;
              }
              .group_14 {
                position: absolute;
                left: 7.547rem;
                top: -0.213rem;
                width: 0.4rem;
                height: 0.587rem;
                background: url(/static/lanhu_huiyuanjiemian_1/6456d02b8b73459b9038769094e48df1_mergeImage.png)
                  100% no-repeat;
                background-size: 100% 100%;
              }
              .group_15 {
                position: absolute;
                left: 8.267rem;
                top: -0.213rem;
                width: 0.4rem;
                height: 0.587rem;
                background: url(/static/lanhu_huiyuanjiemian_1/b77255305c91446bbf19b6699904f13e_mergeImage.png)
                  100% no-repeat;
                background-size: 100% 100%;
              }
            }
          }
          .image-wrapper_2 {
            background-image: linear-gradient(
              179deg,
              rgba(255, 234, 193, 1) 0,
              rgba(235, 193, 162, 1) 100%
            );
            border-radius: 50%;
            height: 1.494rem;
            border: 2px gradient;
            width: 1.494rem;
            position: absolute;
            left: 0.374rem;
            top: 0.32rem;
            .label_1 {
              width: 0.854rem;
              height: 1.12rem;
              margin: 0.187rem 0 0 0.32rem;
            }
          }
        }
      }
      .group_16 {
        background-image: linear-gradient(
          179deg,
          rgba(248, 236, 197, 1) 0,
          rgba(231, 151, 93, 1) 100%
        );
        border-radius: 50%;
        height: 0.934rem;
        border: 1px gradient;
        width: 0.934rem;
        position: absolute;
        left: 12.907rem;
        top: 6.987rem;
        .block_4 {
          background-image: linear-gradient(
            138deg,
            rgba(255, 247, 234, 1) 0,
            rgba(244, 162, 98, 1) 100%
          );
          border-radius: 2px;
          height: 0.827rem;
          border: 1px gradient;
          width: 0.827rem;
          margin: 0.08rem 0 0 0.054rem;
          .section_12 {
            height: 0.827rem;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG5f34148596fd027a8f98a27ad3c7dcb3.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 0.827rem;
            .group_17 {
              box-shadow: inset 1px 0px 0px 0px rgba(0, 0, 0, 0.21);
              background-image: linear-gradient(
                143deg,
                rgba(234, 164, 110, 1) 0,
                rgba(241, 171, 118, 1) 100%
              );
              border-radius: 1px;
              height: 0.587rem;
              width: 0.587rem;
              margin: 0.107rem 0 0 0.107rem;
              .box_5 {
                height: 0.587rem;
                background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGee080a99c1f66756c342138bdf7118ce.png)
                  100% no-repeat;
                background-size: 100% 100%;
                width: 0.587rem;
                .section_13 {
                  width: 0.294rem;
                  height: 0.294rem;
                  background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGc1f566eb5fff4bce5920da1fa503760f.png)
                    100% no-repeat;
                  background-size: 100% 100%;
                  margin: 0.187rem 0 0 0.187rem;
                }
              }
            }
          }
        }
      }
      .text-wrapper_16 {
        position: absolute;
        left: 5.467rem;
        top: 12.24rem;
        width: 5.067rem;
        height: 0.64rem;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 0.64rem;
        .text_31 {
          width: 5.067rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 0.426rem;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 0.64rem;
        }
        .text_32 {
          width: 5.067rem;
          height: 0.64rem;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 0.346rem;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 0.64rem;
        }
      }
      .image_2 {
        position: absolute;
        left: 12.56rem;
        top: 5.814rem;
        width: 2.32rem;
        height: 0.854rem;
      }
    }
    .block_5 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 5.04rem;
      top: 24.854rem;
      width: 10rem;
      height: 8.4rem;
      .box_6 {
        background-color: rgba(245, 245, 245, 1);
        width: 10rem;
        height: 0.267rem;
      }
      .text_33 {
        width: 1.707rem;
        height: 0.507rem;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 0.426rem;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 3.2rem;
        margin: 0.24rem 0 0 0.72rem;
      }
      .box_7 {
        width: 8.454rem;
        height: 1.174rem;
        margin: 0.534rem 0 0 0.774rem;
        .image-wrapper_3 {
          background-color: rgba(233, 248, 241, 1);
          border-radius: 50%;
          height: 1.174rem;
          width: 1.174rem;
          .label_2 {
            width: 0.614rem;
            height: 0.614rem;
            margin: 0.267rem 0 0 0.294rem;
          }
        }
        .text-group_3 {
          width: 3.6rem;
          height: 0.96rem;
          margin: 0.08rem 0 0 0.187rem;
          .text_34 {
            width: 1.494rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
          }
          .text-wrapper_17 {
            width: 3.6rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
            margin-top: 0.134rem;
            .text_35 {
              width: 3.6rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2rem;
            }
            .text_36 {
              width: 3.6rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2rem;
            }
          }
        }
        .text-wrapper_18 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 0.8rem;
          width: 1.894rem;
          margin: 0.16rem 0 0 1.6rem;
          .text_37 {
            width: 1.04rem;
            height: 0.427rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.346rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
            margin: 0.187rem 0 0 0.427rem;
          }
        }
      }
      .box_8 {
        width: 8.454rem;
        height: 1.174rem;
        margin: 0.427rem 0 0 0.8rem;
        .label_3 {
          width: 1.174rem;
          height: 1.174rem;
        }
        .text-group_4 {
          width: 3.6rem;
          height: 0.96rem;
          margin: 0.08rem 0 0 0.187rem;
          .text_38 {
            width: 0.747rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
          }
          .text-wrapper_19 {
            width: 3.6rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
            margin-top: 0.134rem;
            .text_39 {
              width: 3.6rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2rem;
            }
            .text_40 {
              width: 3.6rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2rem;
            }
          }
        }
        .text-wrapper_20 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 0.8rem;
          width: 1.894rem;
          margin: 0.16rem 0 0 1.6rem;
          .text_41 {
            width: 1.04rem;
            height: 0.427rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.346rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
            margin: 0.187rem 0 0 0.427rem;
          }
        }
      }
      .box_9 {
        width: 8.454rem;
        height: 1.174rem;
        margin: 0.507rem 0 0 0.8rem;
        .section_14 {
          background-color: rgba(255, 245, 240, 1);
          border-radius: 50%;
          height: 1.174rem;
          width: 1.174rem;
          .box_10 {
            background-color: rgba(255, 104, 83, 1);
            width: 0.694rem;
            height: 0.64rem;
            margin: 0.294rem 0 0 0.24rem;
          }
        }
        .text-group_5 {
          width: 3.6rem;
          height: 0.96rem;
          margin: 0.08rem 0 0 0.187rem;
          .text_42 {
            width: 0.747rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
          }
          .text-wrapper_21 {
            width: 3.6rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
            margin-top: 0.134rem;
            .text_43 {
              width: 3.6rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2rem;
            }
            .text_44 {
              width: 3.6rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2rem;
            }
          }
        }
        .text-wrapper_22 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 0.8rem;
          width: 1.894rem;
          margin: 0.16rem 0 0 1.6rem;
          .text_45 {
            width: 1.04rem;
            height: 0.427rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.346rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
            margin: 0.187rem 0 0 0.427rem;
          }
        }
      }
      .box_11 {
        width: 8.454rem;
        height: 1.174rem;
        margin: 0.534rem 0 0.694rem 0.827rem;
        .image-wrapper_4 {
          background-color: rgba(241, 251, 242, 1);
          border-radius: 50%;
          height: 1.174rem;
          width: 1.174rem;
          .label_4 {
            width: 0.64rem;
            height: 0.64rem;
            margin: 0.267rem 0 0 0.267rem;
          }
        }
        .text-group_6 {
          width: 3.6rem;
          height: 0.96rem;
          margin: 0.08rem 0 0 0.187rem;
          .text_46 {
            width: 1.494rem;
            height: 0.454rem;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 0.373rem;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
          }
          .text-wrapper_23 {
            width: 3.6rem;
            height: 0.374rem;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
            margin-top: 0.134rem;
            .text_47 {
              width: 3.6rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2rem;
            }
            .text_48 {
              width: 3.6rem;
              height: 0.374rem;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 0.32rem;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 3.2rem;
            }
          }
        }
        .text-wrapper_24 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 0.8rem;
          width: 1.894rem;
          margin: 0.16rem 0 0 1.6rem;
          .text_49 {
            width: 1.04rem;
            height: 0.427rem;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 0.346rem;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 3.2rem;
            margin: 0.187rem 0 0 0.427rem;
          }
        }
      }
    }
  }
}
