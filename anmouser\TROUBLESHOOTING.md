# 故障排除指南

本文档记录了项目开发过程中遇到的常见问题及解决方案。

## 快速参考

### 🚀 快速启动项目（推荐流程）
```bash
# 1. 检查环境
node --version  # 确认 Node.js 版本

# 2. 清理环境（如果之前安装失败）
Remove-Item -Recurse -Force node_modules, package-lock.json -ErrorAction SilentlyContinue
npm cache clean --force

# 3. 确保 package.json 中使用 sass 而不是 node-sass
# "sass": "^1.32.0" ✅
# "node-sass": "^4.12.0" ❌

# 4. 安装依赖
npm install --legacy-peer-deps
npm install @dcloudio/uni-cli-i18n --legacy-peer-deps

# 5. 修复 SCSS 语法错误（如果存在）
# 查找问题文件：
Get-ChildItem -Path "src" -Recurse -Include "*.scss" | Select-String -Pattern "color:\s*;"

# 6. 启动项目
npm run dev:h5
```

### 🔧 常见错误速查表
| 错误信息 | 原因 | 解决方案 |
|---------|------|----------|
| `Node Sass version X.X.X is incompatible` | node-sass 版本不兼容 | 替换为 sass |
| `ERESOLVE unable to resolve dependency tree` | 依赖版本冲突 | 使用 `--legacy-peer-deps` |
| `Cannot resolve module '@dcloudio/uni-cli-i18n'` | 缺少 uniapp 模块 | `npm install @dcloudio/uni-cli-i18n --legacy-peer-deps` |
| `SassError: Expected expression. color: ;` | SCSS 语法错误 | 将 `color: ;` 改为 `color: #000;` |
| `Port 8080 is already in use` | 端口被占用 | 项目会自动使用下一个可用端口 |

## 依赖安装问题

### 1. node-sass 编译失败

**错误信息：**
```bash
Error: Node Sass version 4.14.1 is incompatible with ^4.0.0
gyp ERR! build error
```

**原因：**
- Node.js 版本过新（v22.16.0）与 node-sass v4.14.1 不兼容
- node-sass 已停止维护，建议使用 sass

**解决步骤：**
1. 卸载 node-sass：
   ```bash
   npm uninstall node-sass
   ```

2. 修改 `package.json`：
   ```json
   // 修改前
   "node-sass": "^4.12.0"
   
   // 修改后
   "sass": "^1.32.0"
   ```

3. 重新安装：
   ```bash
   npm install --legacy-peer-deps
   ```

### 2. 依赖版本冲突

**错误信息：**
```bash
ERESOLVE unable to resolve dependency tree
npm ERR! peer dep missing
```

**解决方案：**
使用 `--legacy-peer-deps` 参数强制安装：
```bash
npm install --legacy-peer-deps
```

### 3. 缺少 uniapp 相关模块

**错误信息：**
```bash
Cannot resolve module '@dcloudio/uni-cli-i18n'
```

**解决方案：**
```bash
npm install @dcloudio/uni-cli-i18n --legacy-peer-deps
```

## 编译问题

### 1. SCSS 语法错误

**错误信息：**
```bash
Error: Expected expression
    color: ;
```

**原因：**
设计稿自动生成的代码中存在空的 CSS 属性

**解决方案：**
批量修复空的 `color:` 属性：
```scss
// 错误写法
.text {
  color: ;
}

// 正确写法
.text {
  color: #000;
}
```

**涉及文件：**
- `src/pages/lanhu_huiyuanjiemian_2/assets/style/index.rpx.scss`
- `src/pages/lanhu_dingdanjiemian_1/assets/style/index.rpx.scss`
- 等多个页面样式文件

### 2. Sass 弃用警告

**警告信息：**
```bash
Deprecation Warning [import]: Sass @import rules are deprecated
Deprecation Warning [legacy-js-api]: The legacy JS API is deprecated
```

**说明：**
- 这些是 Sass 版本升级的弃用警告
- 不影响项目正常运行
- 可以暂时忽略，等待 uniapp 官方更新



## 启动问题

### 1. 端口被占用

**错误信息：**
```bash
Port 8080 is already in use
```

**解决方案：**
- 检查其他项目是否占用端口
- 或者项目会自动使用下一个可用端口（如 8082）

### 2. 编译卡住

**现象：**
- `npm install` 过程中长时间无响应
- 编译过程卡在某个步骤

**解决方案：**
1. 终止当前进程（Ctrl+C）
2. 清理缓存：
   ```bash
   npm cache clean --force
   ```
3. 删除依赖重新安装：
   ```bash
   rm -rf node_modules package-lock.json
   npm install --legacy-peer-deps
   ```

## 完整的问题解决流程

当遇到任何问题时，建议按以下顺序操作：

### 第一步：环境检查
```bash
# 检查 Node.js 版本
node --version

# 检查 npm 版本
npm --version
```

### 第二步：清理环境
```bash
# 删除依赖
rm -rf node_modules package-lock.json

# 清理 npm 缓存
npm cache clean --force
```

### 第三步：修改配置
检查并修改 `package.json`：
```json
{
  "devDependencies": {
    "sass": "^1.32.0",
    "sass-loader": "^8.0.2"
  }
}
```

### 第四步：重新安装
```bash
# 安装依赖
npm install --legacy-peer-deps

# 安装缺失模块
npm install @dcloudio/uni-cli-i18n --legacy-peer-deps
```

### 第五步：修复代码问题
1. 修复 SCSS 语法错误
2. 添加布局修复样式

### 第六步：启动项目
```bash
npm run dev:h5
```

## 预防措施

1. **使用正确的依赖**：始终使用 `sass` 而不是 `node-sass`
2. **版本锁定**：在 `package.json` 中锁定关键依赖版本
3. **定期更新**：关注 uniapp 官方更新，及时升级
4. **代码检查**：使用 ESLint 和 Stylelint 检查代码质量

## 实际解决案例记录

### 案例1：完整的项目启动流程（2024年实际解决）

**环境信息：**
- Node.js: v22.16.0
- npm: 10.9.2
- 操作系统: Windows

**遇到的问题：**
1. node-sass 与 Node.js v22.16.0 不兼容
2. 多个 SCSS 文件存在空的 `color:` 属性导致编译失败
3. 缺少 uniapp 相关依赖模块

**完整解决步骤：**

1. **修改 package.json 依赖**
   ```bash
   # 将 node-sass 替换为 sass
   # 在 package.json 中修改：
   # "node-sass": "^4.12.0" → "sass": "^1.32.0"
   ```

2. **清理环境**
   ```bash
   # Windows PowerShell
   Remove-Item -Recurse -Force node_modules, package-lock.json -ErrorAction SilentlyContinue
   npm cache clean --force
   ```

3. **重新安装依赖**
   ```bash
   npm install --legacy-peer-deps
   npm install @dcloudio/uni-cli-i18n --legacy-peer-deps
   ```

4. **批量修复 SCSS 语法错误**

   需要修复的文件列表（将 `color: ;` 替换为 `color: #000;`）：
   - `src/pages/lanhu_huiyuanjiemian_2/assets/style/index.rpx.scss` (第1137行)
   - `src/pages/lanhu_dingdanjiemian_1/assets/style/index.rpx.scss` (第123行)
   - `src/pages/lanhu_chefeitixian/assets/style/index.rpx.scss` (第261行)
   - `src/pages/lanhu_huiyuanjiemian/assets/style/index.rpx.scss` (第771行)
   - `src/pages/lanhu_shenqingtuikuan/assets/style/index.rpx.scss` (第140行)
   - `src/pages/lanhu_jujuetuikuan/assets/style/index.rpx.scss` (第141行)
   - `src/pages/lanhu_shenqingtuikuan_3/assets/style/index.rpx.scss` (第148行)
   - `src/pages/lanhu_tixian/assets/style/index.rpx.scss` (第261行)
   - `src/pages/lanhu_tixian_1/assets/style/index.rpx.scss` (第261行)
   - `src/pages/lanhu_tixian_2/assets/style/index.rpx.scss` (第261行)
   - `src/pages/membershipPage/assets/style/index.rpx.scss` (第850行)

   **批量查找命令（PowerShell）：**
   ```bash
   Get-ChildItem -Path "src" -Recurse -Include "*.scss" | Select-String -Pattern "color:\s*;"
   ```

5. **启动项目**
   ```bash
   npm run dev:h5
   ```

**最终结果：**
- ✅ 项目成功编译并启动
- ✅ 本地访问：http://localhost:8080/
- ✅ 网络访问：http://***********:8080/
- ⚠️ 存在 Sass 弃用警告（不影响运行）

**关键经验总结：**
1. **依赖替换**：node-sass 已停止维护，必须使用 sass
2. **批量修复**：使用命令行工具快速定位所有问题文件
3. **编译监控**：修复过程中实时观察编译错误变化
4. **版本兼容**：新版本 Node.js 需要使用 --legacy-peer-deps 参数

### 案例2：常见的快速修复脚本

**创建自动修复脚本（fix-scss.ps1）：**
```powershell
# 批量修复空的 color 属性
Get-ChildItem -Path "src" -Recurse -Include "*.scss" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    if ($content -match "color:\s*;") {
        $content = $content -replace "color:\s*;", "color: #000;"
        Set-Content -Path $_.FullName -Value $content -NoNewline
        Write-Host "Fixed: $($_.FullName)"
    }
}
```

**使用方法：**
```bash
# 在项目根目录执行
powershell -ExecutionPolicy Bypass -File fix-scss.ps1
```

## 联系支持

如果以上方案都无法解决问题，请：
1. 检查 uniapp 官方文档
2. 查看 GitHub Issues
3. 联系项目维护人员
4. 参考本文档的实际解决案例

---

## 文档更新日志

### 2024-07-23
- ✅ 添加快速参考部分，包含推荐启动流程和错误速查表
- ✅ 新增实际解决案例记录，详细记录了完整的问题解决过程
- ✅ 添加批量修复 SCSS 语法错误的 PowerShell 脚本
- ✅ 补充了 Node.js v22.16.0 环境下的完整解决方案
- ✅ 记录了11个需要修复的 SCSS 文件及其具体行号
- ✅ 提供了项目成功启动的验证方法（本地和网络访问地址）

### 历史版本
- 初始版本：记录了基础的依赖安装和编译问题解决方案
