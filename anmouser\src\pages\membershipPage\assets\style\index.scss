.page {
  background-color: rgba(11, 11, 11, 1);
  position: relative;
  width: 375px;
  height: 1072px;
  overflow: hidden;
  .box_1 {
    background-color: rgba(43, 47, 54, 1);
    width: 375px;
    height: 628px;
    margin-top: 667px;
  }
  .box_2 {
    position: absolute;
    left: -189px;
    top: -175px;
    width: 687px;
    height: 987px;
    background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG1b24b8fb2f4951617799f0f75436778b.png)
      100% no-repeat;
    background-size: 100% 100%;
    .block_1 {
      background-color: rgba(255, 255, 255, 1);
      height: 153px;
      width: 376px;
      margin: 780px 0 0 188px;
      .text-wrapper_1 {
        width: 64px;
        height: 19px;
        margin: 18px 0 0 28px;
        .text_1 {
          width: 64px;
          height: 19px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 16px;
          font-family: Ping<PERSON>ang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 120px;
        }
      }
      .section_1 {
        width: 307px;
        height: 49px;
        margin: 13px 0 0 36px;
        .section_2 {
          background-image: linear-gradient(
            136deg,
            rgba(246, 200, 101, 1) 0,
            rgba(240, 177, 76, 1) 100%
          );
          border-radius: 50%;
          width: 49px;
          height: 49px;
        }
        .section_3 {
          background-image: linear-gradient(
            148deg,
            rgba(239, 144, 133, 1) 0,
            rgba(238, 124, 104, 1) 100%
          );
          border-radius: 50%;
          width: 49px;
          height: 49px;
          margin-left: 37px;
        }
        .section_4 {
          background-image: linear-gradient(
            141deg,
            rgba(112, 193, 126, 1) 0,
            rgba(78, 177, 111, 1) 100%
          );
          border-radius: 50%;
          width: 49px;
          height: 49px;
          margin-left: 37px;
        }
        .section_5 {
          background-image: linear-gradient(
            180deg,
            rgba(255, 185, 186, 1) 0,
            rgba(248, 161, 162, 1) 100%
          );
          border-radius: 50%;
          width: 49px;
          height: 49px;
          margin-left: 37px;
        }
      }
      .section_6 {
        width: 314px;
        height: 35px;
        margin: 3px 0 16px 32px;
        .text-wrapper_2 {
          width: 59px;
          height: 35px;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 134px;
          .paragraph_1 {
            width: 59px;
            height: 35px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 134px;
          }
          .text_2 {
            width: 59px;
            height: 35px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 134px;
          }
        }
        .text-wrapper_3 {
          width: 60px;
          height: 35px;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 134px;
          margin-left: 21px;
          .paragraph_2 {
            width: 60px;
            height: 35px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 134px;
          }
          .text_3 {
            width: 60px;
            height: 35px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 134px;
          }
        }
        .text-wrapper_4 {
          width: 56px;
          height: 35px;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 134px;
          margin-left: 37px;
          .paragraph_3 {
            width: 56px;
            height: 35px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 134px;
          }
          .text_4 {
            width: 56px;
            height: 35px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 134px;
          }
        }
        .text-wrapper_5 {
          width: 56px;
          height: 35px;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: center;
          line-height: 134px;
          margin-left: 25px;
          .paragraph_4 {
            width: 56px;
            height: 35px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            line-height: 134px;
          }
          .text_5 {
            width: 56px;
            height: 35px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: center;
            line-height: 134px;
          }
        }
      }
    }
    .block_2 {
      background-color: rgba(255, 255, 255, 1);
      border-radius: 20px 20px 0px 0px;
      width: 375px;
      height: 338px;
      margin: 443px 123px 0 -375px;
      .section_7 {
        width: 342px;
        height: 131px;
        margin: 58px 0 0 16px;
        .group_1 {
          background-color: rgba(255, 244, 233, 1);
          border-radius: 10px;
          position: relative;
          width: 110px;
          height: 131px;
          border: 2px solid rgba(216, 177, 145, 1);
          .text_6 {
            width: 56px;
            height: 24px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24px;
            margin: 11px 0 0 27px;
          }
          .text-wrapper_6 {
            width: 60px;
            height: 24px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24px;
            margin: 3px 0 0 25px;
            .text_7 {
              width: 60px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 16px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24px;
            }
            .text_8 {
              width: 60px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 24px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 24px;
            }
          }
          .text_9 {
            width: 34px;
            height: 24px;
            overflow-wrap: break-word;
            color: rgba(174, 177, 183, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            text-decoration: line-through;
            font-weight: NaN;
            text-align: left;
            line-height: 24px;
            margin: 5px 0 0 38px;
          }
          .group_2 {
            background-image: linear-gradient(
              90deg,
              rgba(240, 193, 170, 1) 0,
              rgba(215, 176, 143, 1) 100%
            );
            border-radius: 10px;
            width: 76px;
            height: 21px;
            margin: 5px 0 14px 17px;
          }
          .text_10 {
            position: absolute;
            left: 30px;
            top: 94px;
            width: 51px;
            height: 24px;
            overflow-wrap: break-word;
            color: rgba(151, 96, 58, 1);
            font-size: 12px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            line-height: 24px;
          }
        }
        .group_3 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 110px;
          height: 131px;
          border: 2px solid rgba(228, 228, 228, 1);
          margin-left: 6px;
          .text-group_1 {
            width: 70px;
            height: 80px;
            margin: 11px 0 0 20px;
            .text_11 {
              width: 70px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 14px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24px;
            }
            .text-wrapper_7 {
              width: 60px;
              height: 24px;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24px;
              margin: 3px 0 0 5px;
              .text_12 {
                width: 60px;
                height: 24px;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 16px;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 24px;
              }
              .text_13 {
                width: 60px;
                height: 24px;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 24px;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 24px;
              }
            }
            .text_14 {
              width: 34px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(174, 177, 183, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              text-decoration: line-through;
              font-weight: NaN;
              text-align: left;
              line-height: 24px;
              margin: 5px 0 0 18px;
            }
          }
          .text-wrapper_8 {
            background-color: rgba(219, 219, 219, 1);
            border-radius: 10px;
            height: 21px;
            width: 76px;
            margin: 3px 0 16px 17px;
            .text_15 {
              width: 51px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(148, 148, 148, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              line-height: 24px;
              margin-left: 13px;
            }
          }
        }
        .group_4 {
          background-color: rgba(255, 255, 255, 1);
          border-radius: 10px;
          width: 110px;
          height: 131px;
          border: 2px solid rgba(228, 228, 228, 1);
          margin-left: 6px;
          .text-group_2 {
            width: 62px;
            height: 80px;
            margin: 11px 0 0 24px;
            .text_16 {
              width: 56px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(34, 34, 34, 1);
              font-size: 14px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24px;
              margin-left: 3px;
            }
            .text-wrapper_9 {
              width: 62px;
              height: 24px;
              overflow-wrap: break-word;
              font-size: 0;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 24px;
              margin-top: 3px;
              .text_17 {
                width: 62px;
                height: 24px;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 18px;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 24px;
              }
              .text_18 {
                width: 62px;
                height: 24px;
                overflow-wrap: break-word;
                color: rgba(34, 34, 34, 1);
                font-size: 24px;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 24px;
              }
            }
            .text_19 {
              width: 34px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(174, 177, 183, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              text-decoration: line-through;
              font-weight: NaN;
              text-align: left;
              line-height: 24px;
              margin: 5px 0 0 14px;
            }
          }
          .text-wrapper_10 {
            background-color: rgba(219, 219, 219, 1);
            border-radius: 10px;
            height: 21px;
            width: 76px;
            margin: 3px 0 16px 17px;
            .text_20 {
              width: 51px;
              height: 24px;
              overflow-wrap: break-word;
              color: rgba(148, 148, 148, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              line-height: 24px;
              margin-left: 13px;
            }
          }
        }
      }
      .section_8 {
        background-image: linear-gradient(
          90deg,
          rgba(43, 47, 54, 1) 0,
          rgba(101, 95, 92, 1) 100%
        );
        border-radius: 100px;
        width: 348px;
        height: 50px;
        margin: 33px 0 0 13px;
        .text-wrapper_11 {
          width: 60px;
          height: 24px;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24px;
          margin: 13px 0 0 104px;
          .text_21 {
            width: 60px;
            height: 24px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 16px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24px;
          }
          .text_22 {
            width: 60px;
            height: 24px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 24px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 24px;
          }
        }
        .text_23 {
          width: 72px;
          height: 24px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 18px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24px;
          margin: 13px 104px 0 0;
        }
      }
      .section_9 {
        width: 218px;
        height: 24px;
        margin: 14px 0 0 75px;
        .group_5 {
          border-radius: 50%;
          width: 15px;
          height: 15px;
          border: 1px solid rgba(153, 153, 153, 1);
          margin-top: 4px;
        }
        .text-wrapper_12 {
          width: 196px;
          height: 24px;
          overflow-wrap: break-word;
          font-size: 0;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24px;
          .text_24 {
            width: 196px;
            height: 24px;
            overflow-wrap: break-word;
            color: rgba(153, 153, 153, 1);
            font-size: 14px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24px;
          }
          .text_25 {
            width: 196px;
            height: 24px;
            overflow-wrap: break-word;
            color: rgba(215, 175, 142, 1);
            font-size: 14px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 24px;
          }
        }
      }
      .section_10 {
        background-color: rgba(245, 245, 245, 1);
        width: 375px;
        height: 10px;
        margin: 15px 0 3px 0;
      }
    }
    .block_3 {
      position: absolute;
      left: 0;
      top: 0;
      width: 687px;
      height: 444px;
      .group_6 {
        width: 344px;
        height: 19px;
        margin: 182px 0 0 205px;
        .text_26 {
          width: 32px;
          height: 18px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 12px;
          font-family: Inter-Medium;
          font-weight: 500;
          text-align: center;
          white-space: nowrap;
          line-height: 150px;
        }
        .thumbnail_1 {
          width: 18px;
          height: 18px;
          margin-left: 251px;
        }
        .thumbnail_2 {
          width: 18px;
          height: 18px;
          margin-left: 3px;
        }
        .thumbnail_3 {
          width: 19px;
          height: 19px;
          margin-left: 3px;
        }
      }
      .group_7 {
        width: 144px;
        height: 144px;
        margin: 37px 0 62px 190px;
        .section_11 {
          position: relative;
          width: 144px;
          height: 144px;
          background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG48515ac40375b7c83a6436611c40671b.png)
            100% no-repeat;
          background-size: 100% 100%;
          .image-wrapper_1 {
            height: 144px;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG65f5b26c5fff306b2173835ab441c322.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 144px;
            position: absolute;
            left: 113px;
            top: 0;
            .image_1 {
              width: 144px;
              height: 144px;
            }
          }
          .thumbnail_4 {
            position: absolute;
            left: 17px;
            top: -12px;
            width: 9px;
            height: 17px;
          }
        }
      }
      .text-wrapper_13 {
        position: absolute;
        left: 227px;
        top: 223px;
        width: 64px;
        height: 22px;
        .text_27 {
          width: 64px;
          height: 22px;
          overflow-wrap: break-word;
          color: rgba(255, 255, 255, 1);
          font-size: 16px;
          font-family: Inter-Regular;
          font-weight: NaN;
          text-align: center;
          white-space: nowrap;
          line-height: 22px;
        }
      }
      .group_8 {
        box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 1);
        background-image: linear-gradient(
          154deg,
          rgba(255, 250, 240, 1) 0,
          rgba(206, 158, 120, 1) 100%
        );
        border-radius: 20px;
        height: 151px;
        width: 343px;
        position: absolute;
        left: 205px;
        top: 270px;
        .box_3 {
          box-shadow: inset 1px 1px 0px 0px rgba(255, 255, 255, 1);
          background-image: linear-gradient(
            146deg,
            rgba(255, 222, 157, 1) 0,
            rgba(206, 158, 120, 1) 100%
          );
          border-radius: 20px;
          height: 151px;
          width: 343px;
          position: relative;
          .group_9 {
            width: 66px;
            height: 68px;
            margin: 1px 0 0 277px;
            .box_4 {
              width: 66px;
              height: 68px;
              background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGb293715fa2fe32a97708a7696cac578f.png)
                100% no-repeat;
              background-size: 100% 100%;
            }
          }
          .text-wrapper_14 {
            width: 171px;
            height: 12px;
            margin: 25px 0 45px 17px;
            .text_28 {
              width: 171px;
              height: 12px;
              overflow-wrap: break-word;
              color: rgba(62, 29, 4, 1);
              font-size: 19px;
              font-family: PingFang SC-Medium;
              font-weight: 500;
              text-align: left;
              white-space: nowrap;
              line-height: 12px;
            }
          }
          .group_10 {
            background-image: linear-gradient(
              180deg,
              rgba(255, 240, 223, 1) 0,
              rgba(255, 245, 239, 0) 100%
            );
            position: absolute;
            left: 13px;
            top: -26px;
            width: 73px;
            height: 223px;
          }
          .group_11 {
            background-image: linear-gradient(
              180deg,
              rgba(255, 245, 239, 1) 0,
              rgba(255, 245, 239, 0) 100%
            );
            position: absolute;
            left: 56px;
            top: -123px;
            width: 73px;
            height: 283px;
          }
          .group_12 {
            height: 114px;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG577b88c99c41fa54b92d33e4e334f86b.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 125px;
            position: absolute;
            left: 218px;
            top: 19px;
            .text-wrapper_15 {
              height: 30px;
              background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG45961fda21afd8e4b2646cfb27026d11.png)
                100% no-repeat;
              background-size: 100% 100%;
              width: 90px;
              margin: 66px 0 0 21px;
              .text_29 {
                width: 56px;
                height: 14px;
                overflow-wrap: break-word;
                color: rgba(92, 92, 92, 1);
                font-size: 14px;
                font-family: PingFang SC-Regular;
                font-weight: NaN;
                text-align: left;
                white-space: nowrap;
                line-height: 14px;
                margin: 8px 0 0 17px;
              }
            }
            .group_13 {
              background-color: rgba(232, 191, 155, 0.4);
              height: 35px;
              width: 343px;
              position: absolute;
              left: -218px;
              top: 3px;
              .text_30 {
                background-image: linear-gradient(
                  146deg,
                  rgba(213, 172, 135, 1) 0,
                  rgba(185, 124, 72, 1) 100%
                );
                width: 100px;
                height: 20px;
                overflow-wrap: break-word;
                color: ;
                font-size: 20px;
                font-family: PingFang SC-Medium;
                font-weight: 500;
                text-align: left;
                white-space: nowrap;
                line-height: 20px;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                margin: 9px 0 0 79px;
              }
              .group_14 {
                position: absolute;
                left: 283px;
                top: -8px;
                width: 15px;
                height: 22px;
                background: url(/static/lanhu_huiyuanjiemian_1/6456d02b8b73459b9038769094e48df1_mergeImage.png)
                  100% no-repeat;
                background-size: 100% 100%;
              }
              .group_15 {
                position: absolute;
                left: 310px;
                top: -8px;
                width: 15px;
                height: 22px;
                background: url(/static/lanhu_huiyuanjiemian_1/b77255305c91446bbf19b6699904f13e_mergeImage.png)
                  100% no-repeat;
                background-size: 100% 100%;
              }
            }
          }
          .image-wrapper_2 {
            background-image: linear-gradient(
              179deg,
              rgba(255, 234, 193, 1) 0,
              rgba(235, 193, 162, 1) 100%
            );
            border-radius: 50%;
            height: 56px;
            border: 2px gradient;
            width: 56px;
            position: absolute;
            left: 14px;
            top: 12px;
            .label_1 {
              width: 32px;
              height: 42px;
              margin: 7px 0 0 12px;
            }
          }
        }
      }
      .group_16 {
        background-image: linear-gradient(
          179deg,
          rgba(248, 236, 197, 1) 0,
          rgba(231, 151, 93, 1) 100%
        );
        border-radius: 50%;
        height: 35px;
        border: 1px gradient;
        width: 35px;
        position: absolute;
        left: 484px;
        top: 262px;
        .block_4 {
          background-image: linear-gradient(
            138deg,
            rgba(255, 247, 234, 1) 0,
            rgba(244, 162, 98, 1) 100%
          );
          border-radius: 2px;
          height: 31px;
          border: 1px gradient;
          width: 31px;
          margin: 3px 0 0 2px;
          .section_12 {
            height: 31px;
            background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNG5f34148596fd027a8f98a27ad3c7dcb3.png)
              100% no-repeat;
            background-size: 100% 100%;
            width: 31px;
            .group_17 {
              box-shadow: inset 1px 0px 0px 0px rgba(0, 0, 0, 0.21);
              background-image: linear-gradient(
                143deg,
                rgba(234, 164, 110, 1) 0,
                rgba(241, 171, 118, 1) 100%
              );
              border-radius: 1px;
              height: 22px;
              width: 22px;
              margin: 4px 0 0 4px;
              .box_5 {
                height: 22px;
                background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGee080a99c1f66756c342138bdf7118ce.png)
                  100% no-repeat;
                background-size: 100% 100%;
                width: 22px;
                .section_13 {
                  width: 11px;
                  height: 11px;
                  background: url(/static/lanhu_huiyuanjiemian_1/FigmaDDSSlicePNGc1f566eb5fff4bce5920da1fa503760f.png)
                    100% no-repeat;
                  background-size: 100% 100%;
                  margin: 7px 0 0 7px;
                }
              }
            }
          }
        }
      }
      .text-wrapper_16 {
        position: absolute;
        left: 205px;
        top: 459px;
        width: 190px;
        height: 24px;
        overflow-wrap: break-word;
        font-size: 0;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 24px;
        .text_31 {
          width: 190px;
          height: 24px;
          overflow-wrap: break-word;
          color: rgba(34, 34, 34, 1);
          font-size: 16px;
          font-family: PingFang SC-Medium;
          font-weight: 500;
          text-align: left;
          white-space: nowrap;
          line-height: 24px;
        }
        .text_32 {
          width: 190px;
          height: 24px;
          overflow-wrap: break-word;
          color: rgba(11, 206, 148, 1);
          font-size: 13px;
          font-family: PingFang SC-Regular;
          font-weight: NaN;
          text-align: left;
          white-space: nowrap;
          line-height: 24px;
        }
      }
      .image_2 {
        position: absolute;
        left: 471px;
        top: 218px;
        width: 87px;
        height: 32px;
      }
    }
    .block_5 {
      background-color: rgba(255, 255, 255, 1);
      position: absolute;
      left: 189px;
      top: 932px;
      width: 375px;
      height: 315px;
      .box_6 {
        background-color: rgba(245, 245, 245, 1);
        width: 375px;
        height: 10px;
      }
      .text_33 {
        width: 64px;
        height: 19px;
        overflow-wrap: break-word;
        color: rgba(34, 34, 34, 1);
        font-size: 16px;
        font-family: PingFang SC-Medium;
        font-weight: 500;
        text-align: left;
        white-space: nowrap;
        line-height: 120px;
        margin: 9px 0 0 27px;
      }
      .box_7 {
        width: 317px;
        height: 44px;
        margin: 20px 0 0 29px;
        .image-wrapper_3 {
          background-color: rgba(233, 248, 241, 1);
          border-radius: 50%;
          height: 44px;
          width: 44px;
          .label_2 {
            width: 23px;
            height: 23px;
            margin: 10px 0 0 11px;
          }
        }
        .text-group_3 {
          width: 135px;
          height: 36px;
          margin: 3px 0 0 7px;
          .text_34 {
            width: 56px;
            height: 17px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
          }
          .text-wrapper_17 {
            width: 135px;
            height: 14px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
            margin-top: 5px;
            .text_35 {
              width: 135px;
              height: 14px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 120px;
            }
            .text_36 {
              width: 135px;
              height: 14px;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 120px;
            }
          }
        }
        .text-wrapper_18 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 30px;
          width: 71px;
          margin: 6px 0 0 60px;
          .text_37 {
            width: 39px;
            height: 16px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 13px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
            margin: 7px 0 0 16px;
          }
        }
      }
      .box_8 {
        width: 317px;
        height: 44px;
        margin: 16px 0 0 30px;
        .label_3 {
          width: 44px;
          height: 44px;
        }
        .text-group_4 {
          width: 135px;
          height: 36px;
          margin: 3px 0 0 7px;
          .text_38 {
            width: 28px;
            height: 17px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
          }
          .text-wrapper_19 {
            width: 135px;
            height: 14px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
            margin-top: 5px;
            .text_39 {
              width: 135px;
              height: 14px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 120px;
            }
            .text_40 {
              width: 135px;
              height: 14px;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 120px;
            }
          }
        }
        .text-wrapper_20 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 30px;
          width: 71px;
          margin: 6px 0 0 60px;
          .text_41 {
            width: 39px;
            height: 16px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 13px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
            margin: 7px 0 0 16px;
          }
        }
      }
      .box_9 {
        width: 317px;
        height: 44px;
        margin: 19px 0 0 30px;
        .section_14 {
          background-color: rgba(255, 245, 240, 1);
          border-radius: 50%;
          height: 44px;
          width: 44px;
          .box_10 {
            background-color: rgba(255, 104, 83, 1);
            width: 26px;
            height: 24px;
            margin: 11px 0 0 9px;
          }
        }
        .text-group_5 {
          width: 135px;
          height: 36px;
          margin: 3px 0 0 7px;
          .text_42 {
            width: 28px;
            height: 17px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
          }
          .text-wrapper_21 {
            width: 135px;
            height: 14px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
            margin-top: 5px;
            .text_43 {
              width: 135px;
              height: 14px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 120px;
            }
            .text_44 {
              width: 135px;
              height: 14px;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 120px;
            }
          }
        }
        .text-wrapper_22 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 30px;
          width: 71px;
          margin: 6px 0 0 60px;
          .text_45 {
            width: 39px;
            height: 16px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 13px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
            margin: 7px 0 0 16px;
          }
        }
      }
      .box_11 {
        width: 317px;
        height: 44px;
        margin: 20px 0 26px 31px;
        .image-wrapper_4 {
          background-color: rgba(241, 251, 242, 1);
          border-radius: 50%;
          height: 44px;
          width: 44px;
          .label_4 {
            width: 24px;
            height: 24px;
            margin: 10px 0 0 10px;
          }
        }
        .text-group_6 {
          width: 135px;
          height: 36px;
          margin: 3px 0 0 7px;
          .text_46 {
            width: 56px;
            height: 17px;
            overflow-wrap: break-word;
            color: rgba(34, 34, 34, 1);
            font-size: 14px;
            font-family: PingFang SC-Medium;
            font-weight: 500;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
          }
          .text-wrapper_23 {
            width: 135px;
            height: 14px;
            overflow-wrap: break-word;
            font-size: 0;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
            margin-top: 5px;
            .text_47 {
              width: 135px;
              height: 14px;
              overflow-wrap: break-word;
              color: rgba(102, 102, 102, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 120px;
            }
            .text_48 {
              width: 135px;
              height: 14px;
              overflow-wrap: break-word;
              color: rgba(231, 96, 81, 1);
              font-size: 12px;
              font-family: PingFang SC-Regular;
              font-weight: NaN;
              text-align: left;
              white-space: nowrap;
              line-height: 120px;
            }
          }
        }
        .text-wrapper_24 {
          background-color: rgba(11, 206, 148, 1);
          border-radius: 100px;
          height: 30px;
          width: 71px;
          margin: 6px 0 0 60px;
          .text_49 {
            width: 39px;
            height: 16px;
            overflow-wrap: break-word;
            color: rgba(255, 255, 255, 1);
            font-size: 13px;
            font-family: PingFang SC-Regular;
            font-weight: NaN;
            text-align: left;
            white-space: nowrap;
            line-height: 120px;
            margin: 7px 0 0 16px;
          }
        }
      }
    }
  }
}
