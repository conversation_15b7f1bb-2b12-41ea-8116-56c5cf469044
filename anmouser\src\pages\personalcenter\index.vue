<template>
  <scroll-view class="page-scroll" scroll-y="true" enable-back-to-top="true">
    <view class="page flex-col">
    
    <view class="group_2 flex-col">
      <view class="group_3 flex-col">
        <view class="group_4 flex-row justify-between">
          <view class="image-text_1 flex-row justify-between">
            <image
              class="label_1"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNGeefb5f5aabd1316472c3ff1e925727e1.png"
            />
            <view class="group_5 flex-col justify-between">
              <text class="text-group_1">{{ userInfo.nickname }}</text><!--绑定变量-->
              <view class="group_6 flex-row">
                <view class="block_1 flex-col"></view>
                <text class="text_3">{{ userInfo.level }}</text><!--绑定变量-->
              </view>
            </view>
          </view>
          <image
            class="image_2"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/FigmaDDSSlicePNG76647f6356a350a45ac947b45adc1593.png"
          />
        </view>
        <image
          class="thumbnail_4"
          referrerpolicy="no-referrer"
          src="/static/personalcenter/FigmaDDSSlicePNGacfd7ad2ddafa8bae896d1520482e121.png"
        />
        <view class="group_7 flex-col">
          <view class="group_8 flex-row">
            <view class="image-text_2 flex-row justify-between">
              <image
                class="label_2"
                referrerpolicy="no-referrer"
                src="/static/personalcenter/FigmaDDSSlicePNG4f12a4c3e5599b0184444dd7fc4d1980.png"
              />
              <text class="text-group_2">{{ memberStatusText }}</text><!--绑定变量 会员显示=你已开通会员 非会员显示=开通会员享受更多权益-->
            </view>
            <view class="text-wrapper_1 flex-col" @click="handleMemberClick">
              <text class="text_4">{{ memberButtonText }}</text><!--绑定变量 会员显示=查看会员 非会员显示=开通会员-->
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="group_9 flex-col">
      <view class="box_3 flex-row">
        <view class="image-text_3 flex-col justify-between">
          <image
            class="label_3"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/ad35a5f68eff47fc899bd6b01eb6b33f_mergeImage.png"
          />
          <text class="text-group_3">关注</text>
        </view>
        <view class="image-text_4 flex-col justify-between">
          <image
            class="image_3"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/0e3ecbaa34e94df2b75b5119b600af88_mergeImage.png"
          />
          <text class="text-group_4">收藏</text>
        </view>
        <view class="image-text_5 flex-col justify-between">
          <image
            class="label_4"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/cf9e907a6404467f8df165bc0bff8d48_mergeImage.png"
          />
          <text class="text-group_5">动态</text>
        </view>
        <view class="image-text_6 flex-col justify-between">
          <view class="box_4 flex-col">
            <view class="image-wrapper_1 flex-col">
              <image
                class="label_5"
                referrerpolicy="no-referrer"
                src="/static/personalcenter/FigmaDDSSlicePNGf409f9ed6006935444c0c8f348089697.png"
              />
            </view>
          </view>
          <text class="text-group_6">门店</text>
        </view>
        <view class="image-text_7 flex-col justify-between">
          <image
            class="label_6"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/019a4b9965db48baa4840c636bbee903_mergeImage.png"
          />
          <text class="text-group_7">地图找人</text>
        </view>
      </view>
      <view class="box_5 flex-row">
        <view class="image-text_8 flex-col justify-between">
          <view class="image-wrapper_2 flex-col">
            <image
              class="image_4"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNGbb150eb6a38fbfcbbf55aa117fd4cdf2.png"
            />
          </view>
          <text class="text-group_8">分销经理</text>
        </view>
        <view class="image-text_9 flex-col justify-between">
          <image
            class="label_7"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/8af0123c26ad4f29bf858e4a5d9a5439_mergeImage.png"
          />
          <text class="text-group_9">经纪人</text>
        </view>
        <view class="image-text_10 flex-col justify-between">
          <view class="image-wrapper_3 flex-col">
            <image
              class="image_5"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNGf3485d71924eae3e1d200a94188db74a.png"
            />
          </view>
          <text class="text-group_10">合作商</text>
        </view>
        <view class="image-text_11 flex-col justify-between">
          <view class="image-wrapper_4 flex-col">
            <image
              class="label_8"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNG6fb7c1c25b5a60929b5bdbdc857d68bf.png"
            />
          </view>
          <text class="text-group_11">招商加盟</text>
        </view>
        <view class="image-text_12 flex-col justify-between">
          <view class="image-wrapper_5 flex-col">
            <image
              class="image_6"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNG18d04d9675774849820b4a9fd4dc267f.png"
            />
          </view>
          <text class="text-group_12">地址管理</text>
        </view>
      </view>
      <view class="box_6 flex-row">
        <view class="image-text_13 flex-col justify-between">
          <view class="image-wrapper_6 flex-col">
            <image
              class="label_9"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNG4c9c11407c3c41a31cdee2e6b3b19a07.png"
            />
          </view>
          <text class="text-group_13">问题反馈</text>
        </view>
        <view class="image-text_14 flex-col justify-between">
          <view class="image-wrapper_7 flex-col">
            <image
              class="label_10"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNG053711ba7acd0dd54d19fe36db0d9b41.png"
            />
          </view>
          <text class="text-group_14">联系客服</text>
        </view>
        <view class="image-text_15 flex-col justify-between">
          <view class="image-wrapper_8 flex-col">
            <image
              class="label_11"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNGbc1290565abe84c506c2266e23ecf488.png"
            />
          </view>
          <text class="text-group_15">切换管理员</text>
        </view>
        <view class="image-text_16 flex-col justify-between">
          <image
            class="image_7"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/b179b2f3abd74f12934c0de60d17d968_mergeImage.png"
          />
          <text class="text-group_16">切换技师</text>
        </view>
      </view>
    </view>

    <!-- 项目列表 -->
      <!-- tabs -->
      <view class="group_7 flex-row">
        <view class="tabs-container">
          <view class="tab-item">
            <text class="tab-text" :class="{ 'tab-active': tabsIndex === 0 }" @click="toggleTabs(0)">预约上门</text>
            <view class="tab-indicator" v-if="tabsIndex === 0"></view>
          </view>
          <view class="tab-item">
            <text class="tab-text" :class="{ 'tab-active': tabsIndex === 1 }" @click="toggleTabs(1)">推荐技师</text>
            <view class="tab-indicator" v-if="tabsIndex === 1"></view>
          </view>
          <view class="tab-item">
            <text class="tab-text" :class="{ 'tab-active': tabsIndex === 2 }" @click="toggleTabs(2)">到店服务</text>
            <view class="tab-indicator" v-if="tabsIndex === 2"></view>
          </view>
        </view>
        <view class="image-text_3 flex-row justify-between" @click="toggleLayout">
          <text class="text-group_4">切换</text>
          <image
            class="thumbnail_4"
            referrerpolicy="no-referrer"
            src="/static/userhome/FigmaDDSSlicePNG2027b34b07bbd8306566a9cb77906b41.png"
          />
        </view>
      </view>
      <!-- tabs -->

      <!--技师列表 栅格一行两列-->
      <transition name="fade-slide" mode="out-in">
        <view class="technician-grid" v-if="tabsIndex === 1 && layoutMode === 0" key="grid">
        <view class="technician-card" v-for="technician in technicianList" :key="technician.id">
          <!-- 技师头像背景区域 -->
          <view class="card-avatar">
            <!-- 技师头像图片 -->
            <image
              class="technician-avatar"
              referrerpolicy="no-referrer"
              :src="technician.avatar"
            />
            <view class="time-badge">
              <view class="time-label-wrapper">
                <text class="time-label">最早可约</text>
              </view>
              <text class="time-value">{{ technician.earliestTime }}</text>
            </view>
          </view>

          <!-- 技师信息卡片 -->
          <view class="card-content">
            <!-- 技师姓名和状态 -->
            <view class="technician-header">
              <text class="technician-name">{{ technician.name }}</text>
              <view class="status-badge">
                <text class="status-text">{{ technician.status }}</text>
              </view>
            </view>

            <!-- 评分和服务次数 -->
            <view class="rating-section">
              <view class="rating-star"></view>
              <view class="service-info">
                <text class="rating-score">{{ technician.rating }}</text>
                <text class="service-count">已服务{{ technician.serviceCount }}单</text>
              </view>
            </view>

            <!-- 出行费用 -->
            <view class="travel-fee">
              <image
                class="fee-icon"
                referrerpolicy="no-referrer"
                :src="technician.freeIcon"
              />
              <text class="fee-text">{{ technician.freeTravel ? '免出行费用' : '需出行费用' }}</text>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
              <view class="btn-secondary">
                <text class="btn-text">更多照片</text>
              </view>
              <view class="btn-primary">
                <text class="btn-text">立即预约</text>
              </view>
            </view>

            <!-- 底部图标信息 -->
            <view class="bottom-info">
              <view class="info-item">
                <uni-icons type="chat" size="16" color="#969696"></uni-icons>
                <text class="info-text">{{ technician.comments }}</text>
              </view>
              <view class="info-item">
                <uni-icons type="star" size="16" color="#969696"></uni-icons>
                <text class="info-text">{{ technician.favorites }}</text>
              </view>
              <view class="info-item">
                <uni-icons type="shop" size="16" color="#969696"></uni-icons>
                <text class="info-text">{{ technician.shopType }}</text>
              </view>
            </view>
          </view>
        </view>
        </view>
      </transition>
      <!--技师列表 栅格一行两列-->

      <!--技师列表 栅格一行一列-->
      <transition name="fade-slide" mode="out-in">
        <view class="technician-list-container flex-col" v-if="tabsIndex === 1 && layoutMode === 1" key="list">
        <view
          class="technician-list-item flex-col"
          v-for="(item, index) in technicianList"
          :key="index"
        >
          <view class="technician-info-top flex-row">
            <image
              class="technician-avatar-img"
              referrerpolicy="no-referrer"
              :src="item.avatar"
            />
            <view class="single-row-image flex-col justify-between">
              <view class="technician-name-row flex-row justify-between">
                <text class="technician-name-text" >{{ item.name }}</text>
                <view class="technician-photos-btn flex-col">
                  <text class="technician-photos-text" > 更多照片 </text>
                </view>
              </view>
              <view class="technician-rating-row flex-row justify-between">
                <view class="technician-rating-area flex-row justify-between">
                  <view class="technician-star-icon flex-col"></view>
                  <text class="technician-rating-text" >{{ item.rating }}</text>
                </view>
                <text class="technician-service-text" >已服务{{item.serviceCount}}单</text>
              </view>
            </view>
            <view class="single-row-time flex-col justify-between">
              <view class="technician-time-wrapper flex-col">
                <text class="technician-time-text" >最早可约：{{item.earliestTime}}</text>
              </view>
              <view class="technician-distance-area flex-row justify-between">
                <view class="single-row-distance flex-col">
                  <uni-icons type="location" size="16" color="#0BCE94"></uni-icons>
                </view>
                <text class="technician-distance-text" >{{item.distance}}</text>
              </view>
            </view>
          </view>
          <view class="technician-info-bottom flex-row">
            <view
              class="technician-status-badge flex-col"
              :style="{ background: item.lanhuBg13 }"
            >
              <text class="technician-status-text" >{{ item.status }}</text>
            </view>
            <view class="bottom-info">
              <!--评论-->
              <view class="info-item">
                <uni-icons type="chat" size="20" color="#969696"></uni-icons>
                <text class="info-text">{{ item.comments }}</text>
              </view>

              <!--收藏-->
              <view class="info-item">
                <uni-icons type="star" size="20" color="#969696"></uni-icons>
                <text class="info-text">{{ item.favorites }}</text>
              </view>

              <!--商家-->
              <view class="info-item">
                <uni-icons type="shop" size="20" color="#969696"></uni-icons>
                <text class="info-text">{{ item.shopType }}</text>
              </view>
            </view>
            <view class="technician-book-btn flex-col">
              <text class="technician-book-text" >立即预约</text>
            </view>
          </view>
        </view>
        </view>
      </transition>
      <!--技师列表 栅格一行一列-->

      <!--到店服务栅格布局一行二列-->
      <transition name="fade-slide" mode="out-in">
        <view class="shop-grid" v-if="tabsIndex === 2 && layoutMode === 0" key="shop-grid">
        <view
          class="shop-item flex-col"
          v-for="(shop, index) in shopList"
          :key="index"
        >
          <image
            class="shop-image"
            referrerpolicy="no-referrer"
            :src="shop.shopImage"
          />
          <text class="shop-name">{{ shop.shopName }}</text>
          <view class="rating-section flex-row">
            <view class="star-bg flex-col"></view>
            <text class="rating-text">{{ shop.rating }}</text>
            <image
              class="star-icon"
              referrerpolicy="no-referrer"
              :src="shop.starIcon"
            />
            <text class="hours-text">{{ shop.businessHours }}</text>
          </view>
          <view class="distance-section flex-row justify-between">
            <image
              class="location-icon"
              referrerpolicy="no-referrer"
              src="/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG5a4163fb0783a530dfe7050cf95e5f4c.png"
            />
            <text class="distance-text">{{ shop.distance }}</text>
          </view>
          <view class="status-section flex-row justify-between">
            <view class="status-wrapper flex-col">
              <text class="status-text">{{ shop.status }}</text>
            </view>
            <view class="service-wrapper flex-col">
              <text class="service-text">{{ shop.serviceCount }}</text>
            </view>
          </view>
          <view class="rating-info-wrapper flex-col">
            <text class="rating-info-text">
              {{ shop.ratingInfo }}
            </text>
          </view>
        </view>
        </view>
      </transition>
      <!--到店服务栅格布局一行二列-->

      <!--到店服务一行一列-->
      <transition name="fade-slide" mode="out-in">
        <view class="shop-service-list flex-col" v-if="tabsIndex === 2 && layoutMode === 1" key="shop-list">
        <view
          class="shop-service-item flex-row"
          v-for="(item, index) in shopList"
          :key="index"
        >
          <view class="shop-service-main flex-row justify-between">
            <image
              class="shop-service-image"
              referrerpolicy="no-referrer"
              :src="item.shopImage"
            />
            <view class="shop-service-info flex-col">
              <view class="shop-service-header flex-col justify-between">
                <text class="shop-service-name">{{ item.shopName }}</text>
                <view class="shop-service-rating-time flex-row justify-between">
                  <text class="shop-service-rating">{{ item.rating }}</text>
                  <text class="shop-service-hours">{{ item.businessHours }}</text>
                </view>
              </view>
              <view class="shop-service-divider flex-col"></view>
              <image
                class="shop-service-star"
                referrerpolicy="no-referrer"
                :src="item.starIcon"
              />
              <view class="shop-service-rating-info flex-col">
                <text class="shop-service-rating-text">{{ item.ratingInfo }}</text>
              </view>
            </view>
          </view>
          <view class="shop-service-status flex-col">
            <text class="shop-service-status-text">{{ item.status }}</text>
          </view>
          <view class="shop-service-details flex-col justify-between">
            <view class="shop-service-count flex-col">
              <text class="shop-service-count-text">{{ item.serviceCount }}</text>
            </view>
            <view class="shop-service-distance flex-row justify-between">
              <view class="single-row-distance flex-col">
                  <uni-icons type="location" size="16" color="#0BCE94"></uni-icons>
              </view>
              <text class="shop-service-distance-text">{{ item.distance }}</text>
            </view>
          </view>
        </view>
        </view>
      </transition>
      <!--到店服务一行一列-->

      <!-- 预约上门列表 -->
      <transition name="fade-slide" mode="out-in">
        <view class="list_1 flex-col" v-if="tabsIndex === 0" key="appointment-list">
        <view
          class="list-items_1 flex-row justify-between"
          v-for="(item, index) in loopData0"
          :key="index"
        >
          <view class="image-text_4 flex-row">
            <image
              class="image_2"
              referrerpolicy="no-referrer"
              :src="item.lanhuimage0"
            />
            <view class="text-group_5 flex-col">
              <text class="text_15">{{ item.lanhutext0 }}</text>
              <text class="text_16">{{ item.lanhutext1 }}</text>
              <view class="section_2 flex-row justify-between">
                <view class="text-wrapper_3">
                  <text class="text_17">{{ item.lanhutext2 }}</text>
                  <text class="text_18">{{ item.lanhutext3 }}</text>
                </view>
                <text class="text_19">{{ item.lanhutext4 }}</text>
              </view>
            </view>
            <view class="text-wrapper_4 flex-col">
              <text class="text_20">{{ item.lanhutext5 }}</text>
            </view>
            <view class="box_4 flex-row">
              <view class="block_5 flex-col">
                <view class="section_3 flex-col"></view>
              </view>
              <text class="text_21">{{ item.lanhutext6 }}</text>
            </view>
            <view class="box_5 flex-row">
              <view class="image-text_5 flex-row justify-between">
                <view class="group_9 flex-col"></view>
                <text class="text-group_6">{{ item.lanhutext7 }}</text>
              </view>
            </view>
          </view>
          <view class="text-wrapper_5 flex-col">
            <text class="text_22">{{ item.lanhutext8 }}</text>
          </view>
        </view>
        </view>
      </transition>
      <!-- 预约上门列表 -->
    <!-- 项目列表 -->

    <!--底部导航-->
    <view class="group_15 flex-row justify-around">
      <view class="image-text_20 flex-col justify-between">
        <image
          class="label_12"
          referrerpolicy="no-referrer"
          src="/static/personalcenter/FigmaDDSSlicePNG3a64844a13b7e0453d08677530393353.png"
        />
        <text class="text-group_20">首页</text>
      </view>
      <view class="image-text_21 flex-col justify-between">
        <image
          class="label_13"
          referrerpolicy="no-referrer"
          src="/static/personalcenter/99b6f0d4a9324f1db5762929120e3b3b_mergeImage.png"
        />
        <text class="text-group_21">技师</text>
      </view>
      <view class="image-text_22 flex-col justify-between">
        <image
          class="label_14"
          referrerpolicy="no-referrer"
          src="/static/personalcenter/664bdfde0f9f41f4abfc8c10d56a84fe_mergeImage.png"
        />
        <text class="text-group_22">订单</text>
      </view>
      <view class="image-text_23 flex-col justify-between">
        <image
          class="label_15"
          referrerpolicy="no-referrer"
          src="/static/personalcenter/90b3339871e04d31b896e2941512d032_mergeImage.png"
        />
        <text class="text-group_23">我的</text>
      </view>
    </view>
    <!--底部导航-->

    <view class="group_16 flex-col">
      <view class="box_8 flex-col">
        <view class="block_5 flex-row">
          <text class="text_16">我的订单</text>
          <text class="text_17">全部订单</text>
          <image
            class="thumbnail_6"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/FigmaDDSSlicePNG44dd198f6c521802a160986b9f6b7bda.png"
          />
        </view>
        <view class="block_6 flex-row">
          <view class="image-text_24 flex-col justify-between">
            <view class="box_9 flex-col">
              <view class="text-wrapper_5 flex-col" v-if="orderInfo.pendingPaymentCount > 0">
                <text class="text_18">{{ orderInfo.pendingPaymentCount }}</text>
              </view>
            </view>
            <text class="text-group_24">待支付</text>
          </view>
          <view class="image-text_25 flex-col justify-between">
            <image
              class="label_16"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/1e6e9705c535475d9458e11e09c31c8c_mergeImage.png"
            />
            <text class="text-group_25">待服务</text>
          </view>
          <view class="image-text_26 flex-col justify-between">
            <image
              class="label_17"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/7025ea378cab49968e49354ae6d220a6_mergeImage.png"
            />
            <text class="text-group_26">服务中</text>
          </view>
          <view class="image-text_27 flex-col justify-between">
            <image
              class="label_18"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/ba78a2d58e5e40f59eb3dcd1815589f4_mergeImage.png"
            />
            <text class="text-group_27">待评价</text>
          </view>
          <view class="image-text_28 flex-col justify-between">
            <image
              class="label_19"
              referrerpolicy="no-referrer"
              src="/static/personalcenter/FigmaDDSSlicePNGc4edb88dea16f6429c06c4faa0b431ce.png"
            />
            <text class="text-group_28">退款/售后</text>
          </view>
        </view>
      </view>
      <view class="box_10 flex-row">
        <view class="image-text_29 flex-row justify-between">
          <view class="group_17 flex-col">
            <view class="group_18 flex-col">
              <view class="box_11 flex-col"></view>
            </view>
          </view>
          <view class="text-group_29 flex-col justify-between">
            <text class="text_19">邀请好友获好礼</text>
            <text class="text_20">不止于按摩&nbsp;还有推拿&nbsp;做自己</text>
          </view>
        </view>
        <view class="text-wrapper_6 flex-col">
          <text class="text_21">立即推荐</text>
        </view>
      </view>
      <view class="box_12 flex-row">
        <view class="box_13 flex-row">
          <view class="image-text_30 flex-row justify-between">
            <view class="text-group_30 flex-col justify-between">
              <text class="text_22">我的余额</text>
              <text class="text_23">{{ formattedBalance }}</text>
            </view>
            <view class="text-wrapper_7 flex-col">
              <text class="text_24">充值</text>
            </view>
            <view class="section_1 flex-col"></view>
          </view>
        </view>
        <view class="box_14 flex-row">
          <view class="image-text_31 flex-row justify-between">
            <view class="text-group_31 flex-col justify-between">
              <text class="text_25">我的优惠券</text>
              <view class="text-wrapper_8">
                <text class="text_26">{{ orderInfo.couponCount }}</text>
                <text class="text_27">张</text>
              </view>
            </view>
            <view class="text-wrapper_9 flex-col">
              <text class="text_28">查看</text>
            </view>
          </view>
          <image
            class="thumbnail_7"
            referrerpolicy="no-referrer"
            src="/static/personalcenter/FigmaDDSSlicePNG7b7edc4d6a6092c372232723f3d7f451.png"
          />
        </view>
      </view>
    </view>
  </view>
  </scroll-view>
</template>
<script>
export default {
  data() {
    return {
      // 技师列表数据
      technicianList: [
        {
          id: 1,
          name: '王艳艳',
          status: '可预约',
          earliestTime: '11:00',
          rating: 5,
          serviceCount: 489,
          freeTravel: true,
          comments: 0,
          favorites: 0,
          shopType: '商家',
          distance: '0.26km',
          lanhuBg13: 'rgba(11,206,148,1.000000)',
          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',
          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',
          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',
          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG10f0b5a89ef006ddaa010e088ddc9e95.png'
        },
        {
          id: 2,
          name: '李美美',
          status: '不可预约',
          earliestTime: '12:00',
          rating: 4.8,
          serviceCount: 356,
          freeTravel: false,
          comments: 5,
          favorites: 12,
          shopType: '商家',
          distance: '0.26km',
          lanhuBg13: 'rgba(153, 153, 153, 1)',
          lanhuimage1:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNGd1ee15fc2ac6826faff86d6122434868.png',
          lanhuimage2:'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG18ba3b55b0fc8031440ec66e6e6398b9.png',
          avatar: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8798bf9262aed369d3d0b39084d8140a.png',
          freeIcon: '/static/lanhu_shouyejishiliebiao/FigmaDDSSlicePNG8ff91852365737b86422245e75027644.png'
        }
      ],
      // 控制技师列表显示
      tabsIndex: 0,
      // 控制技师列表布局模式：0=一行两列，1=一行一列
      layoutMode: 0,
      //店铺列表
      shopList: [
        {
          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',
          shopName: '爱上你spa按摩...',
          rating: '5',
          businessHours: '00:00-12:15',
          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',
          ratingInfo: '好评率 11%  接单率 11%',
          status: '营业中',
          serviceCount: '1+次服务',
          distance: '0.26KM'
        },
        {
          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',
          shopName: '按摩足浴店新...',
          rating: '5',
          businessHours: '00:00-12:15',
          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',
          ratingInfo: '好评率 11%  接单率 11%',
          status: '营业中',
          serviceCount: '1+次服务',
          distance: '0.26KM'
        },
        {
          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',
          shopName: '康复理疗中心...',
          rating: '4',
          businessHours: '09:00-22:00',
          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',
          ratingInfo: '好评率 95%  接单率 88%',
          status: '营业中',
          serviceCount: '50+次服务',
          distance: '1.2KM'
        },
        {
          shopImage: '/static/lanhu_mendianfuwu_1/FigmaDDSSlicePNG84d787681705e20ebdc3e58e497a1425.png',
          shopName: '康复理疗中心...',
          rating: '4',
          businessHours: '09:00-22:00',
          starIcon: 'https://lanhu-oss-2537-2.lanhuapp.com/FigmaDDSSlicePNG6472fe19b1ea14af02091324d9cccbbf.png',
          ratingInfo: '好评率 95%  接单率 88%',
          status: '营业中',
          serviceCount: '50+次服务',
          distance: '1.2KM'
        }
      ],
      // AUGMENT: 用户信息相关数据
      userInfo: {
        nickname: '爱莫能助', // 用户昵称
        level: 1, // 用户等级
        isVip: false // 是否为会员
      },
      // AUGMENT: 订单和财务相关数据
      orderInfo: {
        pendingPaymentCount: 1, // 待支付订单数量
        balance: 2354.35, // 账户余额
        couponCount: 2 // 优惠券数量
      },
      constants: {}
    };
  },
  // AUGMENT: 计算属性
  computed: {
    // 会员状态文本
    memberStatusText() {
      return this.userInfo.isVip ? '你已开通会员' : '开通会员享受更多权益';
    },
    // 会员按钮文本
    memberButtonText() {
      return this.userInfo.isVip ? '查看会员' : '开通会员';
    },
    // 格式化余额显示
    formattedBalance() {
      const balance = this.orderInfo.balance;
      if (balance >= 10000) {
        // 大于等于1万时，显示为 "X.X万"
        const wan = (balance / 10000).toFixed(1);
        return `${wan}万`;
      } else {
        // 小于1万时，显示原始数字，保留两位小数
        return balance.toFixed(2);
      }
    }
  },
  onLoad() {
    // 页面加载时读取缓存的布局模式
    this.loadLayoutMode();
  },
  methods: {
    // 切换技师列表显示状态
    toggleTabs(index) {
      this.tabsIndex = index;
    },
    // 切换技师列表布局模式
    toggleLayout() {
      this.layoutMode = this.layoutMode === 0 ? 1 : 0;
      // 保存布局模式到缓存
      this.saveLayoutMode();
    },
    // 保存布局模式到本地缓存
    saveLayoutMode() {
      try {
        uni.setStorageSync('technicianLayoutMode', this.layoutMode);
      } catch (error) {
        console.error('保存布局模式失败:', error);
      }
    },
    // 从本地缓存读取布局模式
    loadLayoutMode() {
      try {
        const savedLayoutMode = uni.getStorageSync('technicianLayoutMode');
        if (savedLayoutMode !== '' && savedLayoutMode !== null && savedLayoutMode !== undefined) {
          this.layoutMode = savedLayoutMode;
        }
      } catch (error) {
        console.error('读取布局模式失败:', error);
        // 读取失败时使用默认值
        this.layoutMode = 0;
      }
    },
    // AUGMENT: 用户信息相关方法
    // 切换会员状态（用于测试）
    toggleVipStatus() {
      this.userInfo.isVip = !this.userInfo.isVip;
    },
    // 更新用户昵称
    updateNickname(newNickname) {
      this.userInfo.nickname = newNickname;
    },
    // 更新用户等级
    updateLevel(newLevel) {
      this.userInfo.level = newLevel;
    },
    // AUGMENT: 订单和财务相关方法
    // 更新待支付订单数量
    updatePendingPaymentCount(count) {
      this.orderInfo.pendingPaymentCount = count;
    },
    // 更新账户余额
    updateBalance(balance) {
      this.orderInfo.balance = balance;
    },
    // 更新优惠券数量
    updateCouponCount(count) {
      this.orderInfo.couponCount = count;
    },
    // 模拟清空待支付订单（用于测试徽章隐藏）
    clearPendingPayments() {
      this.orderInfo.pendingPaymentCount = 0;
    },
    // 模拟添加待支付订单（用于测试徽章显示）
    addPendingPayment() {
      this.orderInfo.pendingPaymentCount += 1;
    },
    // AUGMENT: 会员相关方法
    // 处理会员按钮点击
    handleMemberClick() {
      if (this.userInfo.isVip) {
        // 已是会员，跳转到会员详情页
        uni.navigateTo({
          url: '/pages/membershipPage/index'
        });
      } else {
        // 非会员，跳转到开通会员页
        uni.navigateTo({
          url: '/pages/membershipPage/index'
        });
      }
    }
  }
};
</script>
<style lang='scss'>
@import '../common/common.scss';
@import './assets/style/index.rpx.scss';
</style>
